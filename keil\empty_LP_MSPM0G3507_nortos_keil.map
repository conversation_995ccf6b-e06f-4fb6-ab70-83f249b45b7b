Component: Arm Compiler for Embedded 6.23 Tool: armlink [5f102400]

==============================================================================

Section Cross References

    empty.o(.text.main) refers to board.o(.text.board_init) for board_init
    empty.o(.text.main) refers to zdt.o(.text.Motor_Init) for Motor_Init
    empty.o(.text.main) refers to zdt.o(.text.Motor_SetPosition) for Motor_SetPosition
    empty.o(.text.main) refers to zdt.o(.text.Motor_Run) for Motor_Run
    empty.o(.text.main) refers to zdt.o(.bss.Stop_Flag_Car) for Stop_Flag_Car
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.text.Read_Key) refers to key.o(.text.Key_Read) for Key_Read
    empty.o(.text.Read_Key) refers to empty.o(.bss.key24_val) for key24_val
    empty.o(.text.Read_Key) refers to empty.o(.bss.key25_val) for key25_val
    empty.o(.text.Read_Key) refers to empty.o(.bss.key26_val) for key26_val
    empty.o(.text.Read_Key) refers to empty.o(.bss.key27_val) for key27_val
    empty.o(.ARM.exidx.text.Read_Key) refers to empty.o(.text.Read_Key) for [Anonymous Symbol]
    empty.o(.text.UiDispatcher) refers to xunji.o(.text.my_GPIO_readPin) for my_GPIO_readPin
    empty.o(.text.UiDispatcher) refers to hw_lcd.o(.text.LCD_Fill) for LCD_Fill
    empty.o(.text.UiDispatcher) refers to empty.o(.text.handle_ui_home_one) for handle_ui_home_one
    empty.o(.text.UiDispatcher) refers to empty.o(.text.handle_ui_left_right_page) for handle_ui_left_right_page
    empty.o(.text.UiDispatcher) refers to empty.o(.text.handle_ui_question_page) for handle_ui_question_page
    empty.o(.text.UiDispatcher) refers to empty.o(.bss.key24_val) for key24_val
    empty.o(.text.UiDispatcher) refers to empty.o(.bss.last_key24_val) for last_key24_val
    empty.o(.text.UiDispatcher) refers to empty.o(.bss.Ui_Index) for Ui_Index
    empty.o(.text.UiDispatcher) refers to empty.o(.bss.Ui_Disp) for Ui_Disp
    empty.o(.text.UiDispatcher) refers to empty.o(.bss.key27_val) for key27_val
    empty.o(.text.UiDispatcher) refers to empty.o(.bss.last_key27_val) for last_key27_val
    empty.o(.ARM.exidx.text.UiDispatcher) refers to empty.o(.text.UiDispatcher) for [Anonymous Symbol]
    empty.o(.text.handle_ui_home_one) refers to app_ui.o(.text.ui_home_One) for ui_home_One
    empty.o(.ARM.exidx.text.handle_ui_home_one) refers to empty.o(.text.handle_ui_home_one) for [Anonymous Symbol]
    empty.o(.text.handle_ui_left_right_page) refers to app_ui.o(.text.ui_left_right_page) for ui_left_right_page
    empty.o(.ARM.exidx.text.handle_ui_left_right_page) refers to empty.o(.text.handle_ui_left_right_page) for [Anonymous Symbol]
    empty.o(.text.handle_ui_question_page) refers to app_ui.o(.text.ui_question_page) for ui_question_page
    empty.o(.ARM.exidx.text.handle_ui_question_page) refers to empty.o(.text.handle_ui_question_page) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to encoder.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to wit.o(.text.UART3_IRQHandler) for UART3_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to board.o(.text.UART2_IRQHandler) for UART2_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to board.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for SYSCFG_DL_PWM_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) for SYSCFG_DL_TIMER_TICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for SYSCFG_DL_I2C_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) for SYSCFG_DL_UART_WIT_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) for SYSCFG_DL_UART_2_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) for SYSCFG_DL_SPI_LCD_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for SYSCFG_DL_DMA_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gUART_WITBackup) for gUART_WITBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for DL_Timer_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_I2C_reset) for DL_I2C_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for DL_UART_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_SPI_reset) for DL_SPI_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for DL_Timer_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_I2C_enablePower) for DL_I2C_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for DL_UART_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_SPI_enablePower) for DL_SPI_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for DL_GPIO_initPeripheralAnalogFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for DL_GPIO_initPeripheralOutputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures) for DL_GPIO_initPeripheralInputFunctionFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ) for DL_GPIO_enableHiZ
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for DL_GPIO_initPeripheralInputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for DL_GPIO_initDigitalInputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for DL_GPIO_setLowerPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for DL_GPIO_setUpperPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for DL_GPIO_enableInterrupt
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for DL_SYSCTL_setBORThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for DL_SYSCTL_setSYSOSCFreq
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for DL_SYSCTL_disableHFXT
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for DL_SYSCTL_disableSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for DL_SYSCTL_enableMFCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) for DL_I2C_setAnalogGlitchFilterPulseWidth
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter) for DL_I2C_enableAnalogGlitchFilter
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer) for DL_I2C_resetControllerTransfer
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod) for DL_I2C_setTimerPeriod
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) for DL_I2C_setControllerTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) for DL_I2C_setControllerRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching) for DL_I2C_enableControllerClockStretching
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableController) for DL_I2C_enableController
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for DL_UART_enableDMAReceiveEvent
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for DL_UART_enableFIFOs
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for DL_UART_setRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.text.DL_UART_setRXInterruptTimeout) for DL_UART_setRXInterruptTimeout
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.rodata.gUART_WITClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.rodata.gUART_WITConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_WIT_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.rodata.gUART_2ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.rodata.gUART_2Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for DL_SPI_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to dl_spi.o(.text.DL_SPI_init) for DL_SPI_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) for DL_SPI_setBitRateSerialClockDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) for DL_SPI_setFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.DL_SPI_enable) for DL_SPI_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.rodata.gSPI_LCD_config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_LCD_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init) for SYSCFG_DL_DMA_WIT_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for DL_SYSTICK_enable
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for DL_UART_Main_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for DL_SPI_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gUART_WITBackup) for gUART_WITBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for DL_UART_Main_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for DL_SPI_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gUART_WITBackup) for gUART_WITBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_LCDBackup) for gSPI_LCDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_reset) refers to ti_msp_dl_config.o(.text.DL_I2C_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_reset) refers to ti_msp_dl_config.o(.text.DL_SPI_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enablePower) refers to ti_msp_dl_config.o(.text.DL_I2C_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enablePower) refers to ti_msp_dl_config.o(.text.DL_SPI_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunctionFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableHiZ) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setAnalogGlitchFilterPulseWidth) refers to ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableAnalogGlitchFilter) refers to ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_resetControllerTransfer) refers to ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setTimerPeriod) refers to ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableControllerClockStretching) refers to ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableController) refers to ti_msp_dl_config.o(.text.DL_I2C_enableController) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableDMAReceiveEvent) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setRXInterruptTimeout) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXInterruptTimeout) refers to ti_msp_dl_config.o(.text.DL_UART_setRXInterruptTimeout) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setBitRateSerialClockDivider) refers to ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enable) refers to ti_msp_dl_config.o(.text.DL_SPI_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init) refers to dl_dma.o(.text.DL_DMA_initChannel) for DL_DMA_initChannel
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init) refers to ti_msp_dl_config.o(.rodata.gDMA_WITConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_WIT_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    app_ui.o(.text.disp_x_center_zero) refers to hw_lcd.o(.text.LCD_ArcRect) for LCD_ArcRect
    app_ui.o(.text.disp_x_center_zero) refers to hw_lcd.o(.text.LCD_ShowChinese) for LCD_ShowChinese
    app_ui.o(.ARM.exidx.text.disp_x_center_zero) refers to app_ui.o(.text.disp_x_center_zero) for [Anonymous Symbol]
    app_ui.o(.text.disp_string_rect_zero) refers to hw_lcd.o(.text.LCD_ArcRect) for LCD_ArcRect
    app_ui.o(.text.disp_string_rect_zero) refers to hw_lcd.o(.text.LCD_ShowChinese) for LCD_ShowChinese
    app_ui.o(.ARM.exidx.text.disp_string_rect_zero) refers to app_ui.o(.text.disp_string_rect_zero) for [Anonymous Symbol]
    app_ui.o(.text.disp_select_box_zero) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    app_ui.o(.ARM.exidx.text.disp_select_box_zero) refers to app_ui.o(.text.disp_select_box_zero) for [Anonymous Symbol]
    app_ui.o(.text.draw_starfield) refers to rand.o(.emb_text) for rand
    app_ui.o(.text.draw_starfield) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    app_ui.o(.text.draw_starfield) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    app_ui.o(.ARM.exidx.text.draw_starfield) refers to app_ui.o(.text.draw_starfield) for [Anonymous Symbol]
    app_ui.o(.text.ui_home_page) refers to app_ui.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    app_ui.o(.text.ui_home_page) refers to app_ui.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    app_ui.o(.text.ui_home_page) refers to hw_lcd.o(.text.LCD_Fill) for LCD_Fill
    app_ui.o(.ARM.exidx.text.ui_home_page) refers to app_ui.o(.text.ui_home_page) for [Anonymous Symbol]
    app_ui.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to app_ui.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    app_ui.o(.ARM.exidx.text.DL_GPIO_setPins) refers to app_ui.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    app_ui.o(.text.star_expansion_fullscreen_animation) refers to hw_lcd.o(.text.LCD_Fill) for LCD_Fill
    app_ui.o(.text.star_expansion_fullscreen_animation) refers to rand.o(.emb_text) for rand
    app_ui.o(.text.star_expansion_fullscreen_animation) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    app_ui.o(.text.star_expansion_fullscreen_animation) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    app_ui.o(.text.star_expansion_fullscreen_animation) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    app_ui.o(.text.star_expansion_fullscreen_animation) refers to board.o(.text.delay_ms) for delay_ms
    app_ui.o(.ARM.exidx.text.star_expansion_fullscreen_animation) refers to app_ui.o(.text.star_expansion_fullscreen_animation) for [Anonymous Symbol]
    app_ui.o(.text.ui_home_One) refers to app_ui.o(.text.disp_x_center_zero) for disp_x_center_zero
    app_ui.o(.text.ui_home_One) refers to app_ui.o(.text.disp_string_rect_zero) for disp_string_rect_zero
    app_ui.o(.text.ui_home_One) refers to app_ui.o(.text.disp_select_box_zero) for disp_select_box_zero
    app_ui.o(.text.ui_home_One) refers to app_ui.o(.rodata.str1.1) for [Anonymous Symbol]
    app_ui.o(.text.ui_home_One) refers to empty.o(.bss.key27_val) for key27_val
    app_ui.o(.ARM.exidx.text.ui_home_One) refers to app_ui.o(.text.ui_home_One) for [Anonymous Symbol]
    app_ui.o(.text.ui_left_right_page) refers to hw_lcd.o(.text.LCD_ShowString) for LCD_ShowString
    app_ui.o(.text.ui_left_right_page) refers to hw_lcd.o(.text.LCD_DrawRectangle) for LCD_DrawRectangle
    app_ui.o(.text.ui_left_right_page) refers to strlen.o(.text) for strlen
    app_ui.o(.text.ui_left_right_page) refers to hw_lcd.o(.text.LCD_ShowIntNum) for LCD_ShowIntNum
    app_ui.o(.text.ui_left_right_page) refers to app_ui.o(.rodata.str1.1) for [Anonymous Symbol]
    app_ui.o(.text.ui_left_right_page) refers to empty.o(.bss.key26_val) for key26_val
    app_ui.o(.ARM.exidx.text.ui_left_right_page) refers to app_ui.o(.text.ui_left_right_page) for [Anonymous Symbol]
    app_ui.o(.text.ui_question_page) refers to hw_lcd.o(.text.LCD_ShowString) for LCD_ShowString
    app_ui.o(.text.ui_question_page) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    app_ui.o(.text.ui_question_page) refers to hw_lcd.o(.text.LCD_DrawRectangle) for LCD_DrawRectangle
    app_ui.o(.text.ui_question_page) refers to app_ui.o(.text.disp_select_box_zero) for disp_select_box_zero
    app_ui.o(.text.ui_question_page) refers to app_ui.o(.rodata.str1.1) for [Anonymous Symbol]
    app_ui.o(.text.ui_question_page) refers to empty.o(.bss.key25_val) for key25_val
    app_ui.o(.text.ui_question_page) refers to empty.o(.bss.key26_val) for key26_val
    app_ui.o(.text.ui_question_page) refers to app_ui.o(.bss.ui_question_page.current_index) for [Anonymous Symbol]
    app_ui.o(.ARM.exidx.text.ui_question_page) refers to app_ui.o(.text.ui_question_page) for [Anonymous Symbol]
    encoder.o(.text.encoder_init) refers to encoder.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    encoder.o(.ARM.exidx.text.encoder_init) refers to encoder.o(.text.encoder_init) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to encoder.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    encoder.o(.text.encoder_callback) refers to encoder.o(.bss.left_counter) for left_counter
    encoder.o(.text.encoder_callback) refers to encoder.o(.bss.left_speed) for left_speed
    encoder.o(.text.encoder_callback) refers to encoder.o(.bss.left_distance) for left_distance
    encoder.o(.text.encoder_callback) refers to encoder.o(.bss.right_counter) for right_counter
    encoder.o(.text.encoder_callback) refers to encoder.o(.bss.right_speed) for right_speed
    encoder.o(.text.encoder_callback) refers to encoder.o(.bss.right_distance) for right_distance
    encoder.o(.ARM.exidx.text.encoder_callback) refers to encoder.o(.text.encoder_callback) for [Anonymous Symbol]
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for DL_GPIO_getEnabledInterruptStatus
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.right_counter) for right_counter
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.left_counter) for left_counter
    encoder.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to encoder.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus) refers to encoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_readPins) refers to encoder.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to encoder.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    encoder.o(.text.timer_init) refers to encoder.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    encoder.o(.text.timer_init) refers to encoder.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    encoder.o(.ARM.exidx.text.timer_init) refers to encoder.o(.text.timer_init) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to encoder.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_I2C_Init) refers to oled.o(.text.OLED_I2C_Init) for [Anonymous Symbol]
    oled.o(.text.I2C_WriteByte) refers to oled.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    oled.o(.text.I2C_WriteByte) refers to oled.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    oled.o(.text.I2C_WriteByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    oled.o(.ARM.exidx.text.I2C_WriteByte) refers to oled.o(.text.I2C_WriteByte) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_I2C_getControllerStatus) refers to oled.o(.text.DL_I2C_getControllerStatus) for [Anonymous Symbol]
    oled.o(.text.DL_I2C_startControllerTransfer) refers to oled.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    oled.o(.ARM.exidx.text.DL_I2C_startControllerTransfer) refers to oled.o(.text.DL_I2C_startControllerTransfer) for [Anonymous Symbol]
    oled.o(.text.WriteCmd) refers to oled.o(.text.I2C_WriteByte) for I2C_WriteByte
    oled.o(.ARM.exidx.text.WriteCmd) refers to oled.o(.text.WriteCmd) for [Anonymous Symbol]
    oled.o(.text.WriteData) refers to oled.o(.text.I2C_WriteByte) for I2C_WriteByte
    oled.o(.ARM.exidx.text.WriteData) refers to oled.o(.text.WriteData) for [Anonymous Symbol]
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.WriteData) for WriteData
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.WriteCmd) for WriteCmd
    oled.o(.ARM.exidx.text.OLED_WR_Byte) refers to oled.o(.text.OLED_WR_Byte) for [Anonymous Symbol]
    oled.o(.text.fill_picture) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.fill_picture) refers to oled.o(.text.fill_picture) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.Delay_50ms) refers to oled.o(.text.Delay_50ms) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.Delay_1ms) refers to oled.o(.text.Delay_1ms) for [Anonymous Symbol]
    oled.o(.text.OLED_Set_Pos) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Set_Pos) refers to oled.o(.text.OLED_Set_Pos) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_On) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Display_On) refers to oled.o(.text.OLED_Display_On) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_Off) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Display_Off) refers to oled.o(.text.OLED_Display_Off) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_On) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_On) refers to oled.o(.text.OLED_On) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.F8X16) for F8X16
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.oled_pow) refers to oled.o(.text.oled_pow) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowNumber) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.text.OLED_ShowNumber) refers to oled.o(.text.oled_pow) for oled_pow
    oled.o(.text.OLED_ShowNumber) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled.o(.text.OLED_ShowNumber) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    oled.o(.ARM.exidx.text.OLED_ShowNumber) refers to oled.o(.text.OLED_ShowNumber) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowCHinese) refers to oled.o(.text.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(.text.OLED_ShowCHinese) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_ShowCHinese) refers to oled.o(.data.Hzk) for Hzk
    oled.o(.ARM.exidx.text.OLED_ShowCHinese) refers to oled.o(.text.OLED_ShowCHinese) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawBMP) refers to oled.o(.text.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(.text.OLED_DrawBMP) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_DrawBMP) refers to oled.o(.text.OLED_DrawBMP) for [Anonymous Symbol]
    oled.o(.text.OLED_Float) refers to dcmp.o(i._dgeq) for __aeabi_dcmpge
    oled.o(.text.OLED_Float) refers to dfixi.o(.text) for __aeabi_d2iz
    oled.o(.text.OLED_Float) refers to dflti.o(.text) for __aeabi_i2d
    oled.o(.text.OLED_Float) refers to daddsub.o(.text) for __aeabi_dsub
    oled.o(.text.OLED_Float) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(.text.OLED_Float) refers to dfixui.o(.text) for __aeabi_d2uiz
    oled.o(.text.OLED_Float) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    oled.o(.text.OLED_Float) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    oled.o(.text.OLED_Float) refers to oled.o(.text.OLED_ShowString) for OLED_ShowString
    oled.o(.ARM.exidx.text.OLED_Float) refers to oled.o(.text.OLED_Float) for [Anonymous Symbol]
    oled.o(.text.OLED_Float2) refers to dfixi.o(.text) for __aeabi_d2iz
    oled.o(.text.OLED_Float2) refers to dflti.o(.text) for __aeabi_ui2d
    oled.o(.text.OLED_Float2) refers to daddsub.o(.text) for __aeabi_dsub
    oled.o(.text.OLED_Float2) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(.text.OLED_Float2) refers to dfixui.o(.text) for __aeabi_d2uiz
    oled.o(.text.OLED_Float2) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled.o(.text.OLED_Float2) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    oled.o(.text.OLED_Float2) refers to oled.o(.text.OLED_ShowString) for OLED_ShowString
    oled.o(.ARM.exidx.text.OLED_Float2) refers to oled.o(.text.OLED_Float2) for [Anonymous Symbol]
    oled.o(.text.OLED_Num2) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    oled.o(.text.OLED_Num2) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    oled.o(.text.OLED_Num2) refers to oled.o(.text.OLED_fuhao_write) for OLED_fuhao_write
    oled.o(.text.OLED_Num2) refers to oled.o(.text.OLED_Num_write) for OLED_Num_write
    oled.o(.ARM.exidx.text.OLED_Num2) refers to oled.o(.text.OLED_Num2) for [Anonymous Symbol]
    oled.o(.text.OLED_fuhao_write) refers to oled.o(.text.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(.text.OLED_fuhao_write) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_fuhao_write) refers to oled.o(.rodata.F6x8) for F6x8
    oled.o(.ARM.exidx.text.OLED_fuhao_write) refers to oled.o(.text.OLED_fuhao_write) for [Anonymous Symbol]
    oled.o(.text.OLED_Num_write) refers to oled.o(.text.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(.text.OLED_Num_write) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_Num_write) refers to oled.o(.rodata.F6x8) for F6x8
    oled.o(.ARM.exidx.text.OLED_Num_write) refers to oled.o(.text.OLED_Num_write) for [Anonymous Symbol]
    oled.o(.text.OLED_Num3) refers to oled.o(.text.OLED_fuhao_write) for OLED_fuhao_write
    oled.o(.text.OLED_Num3) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    oled.o(.text.OLED_Num3) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    oled.o(.text.OLED_Num3) refers to oled.o(.text.OLED_Num_write) for OLED_Num_write
    oled.o(.ARM.exidx.text.OLED_Num3) refers to oled.o(.text.OLED_Num3) for [Anonymous Symbol]
    oled.o(.text.OLED_Num4) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    oled.o(.text.OLED_Num4) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    oled.o(.text.OLED_Num4) refers to oled.o(.text.OLED_Num_write) for OLED_Num_write
    oled.o(.ARM.exidx.text.OLED_Num4) refers to oled.o(.text.OLED_Num4) for [Anonymous Symbol]
    oled.o(.text.OLED_Num5) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    oled.o(.text.OLED_Num5) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    oled.o(.text.OLED_Num5) refers to oled.o(.text.OLED_Num_write) for OLED_Num_write
    oled.o(.ARM.exidx.text.OLED_Num5) refers to oled.o(.text.OLED_Num5) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to board.o(.text.delay_ms) for delay_ms
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_Common_updateReg) refers to oled.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    pid.o(.text.PID_Update) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(.text.PID_Update) refers to fcmp.o(i._feq) for __aeabi_fcmpeq
    pid.o(.text.PID_Update) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(.text.PID_Update) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(.text.PID_Update) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    pid.o(.text.PID_Update) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.PID_Update) refers to pid.o(.text.PID_Update) for [Anonymous Symbol]
    pwm.o(.text.TB6612_Motor_Stop) refers to pwm.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    pwm.o(.ARM.exidx.text.TB6612_Motor_Stop) refers to pwm.o(.text.TB6612_Motor_Stop) for [Anonymous Symbol]
    pwm.o(.ARM.exidx.text.DL_GPIO_setPins) refers to pwm.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    pwm.o(.text.AO_Control) refers to pwm.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    pwm.o(.text.AO_Control) refers to pwm.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    pwm.o(.text.AO_Control) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    pwm.o(.ARM.exidx.text.AO_Control) refers to pwm.o(.text.AO_Control) for [Anonymous Symbol]
    pwm.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to pwm.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    pwm.o(.text.BO_Control) refers to pwm.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    pwm.o(.text.BO_Control) refers to pwm.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    pwm.o(.text.BO_Control) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    pwm.o(.ARM.exidx.text.BO_Control) refers to pwm.o(.text.BO_Control) for [Anonymous Symbol]
    pwm.o(.text.motor_set_duty) refers to pwm.o(.text.AO_Control) for AO_Control
    pwm.o(.text.motor_set_duty) refers to pwm.o(.text.BO_Control) for BO_Control
    pwm.o(.ARM.exidx.text.motor_set_duty) refers to pwm.o(.text.motor_set_duty) for [Anonymous Symbol]
    xunji.o(.text.PID_Kongzhi) refers to fflti.o(.text) for __aeabi_i2f
    xunji.o(.text.PID_Kongzhi) refers to pid.o(.text.PID_Update) for PID_Update
    xunji.o(.text.PID_Kongzhi) refers to ffixi.o(.text) for __aeabi_f2iz
    xunji.o(.text.PID_Kongzhi) refers to pwm.o(.text.AO_Control) for AO_Control
    xunji.o(.text.PID_Kongzhi) refers to pwm.o(.text.BO_Control) for BO_Control
    xunji.o(.text.PID_Kongzhi) refers to xunji.o(.data.AnglePID_xunji_zuo) for AnglePID_xunji_zuo
    xunji.o(.text.PID_Kongzhi) refers to xunji.o(.data.AnglePID_xunji_you) for AnglePID_xunji_you
    xunji.o(.text.PID_Kongzhi) refers to xunji.o(.bss.Angle_xunji_zuo) for Angle_xunji_zuo
    xunji.o(.text.PID_Kongzhi) refers to xunji.o(.bss.Angle_xunji_you) for Angle_xunji_you
    xunji.o(.ARM.exidx.text.PID_Kongzhi) refers to xunji.o(.text.PID_Kongzhi) for [Anonymous Symbol]
    xunji.o(.text.my_GPIO_readPin) refers to xunji.o(.text.find_set_bit) for find_set_bit
    xunji.o(.text.my_GPIO_readPin) refers to xunji.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    xunji.o(.ARM.exidx.text.my_GPIO_readPin) refers to xunji.o(.text.my_GPIO_readPin) for [Anonymous Symbol]
    xunji.o(.ARM.exidx.text.find_set_bit) refers to xunji.o(.text.find_set_bit) for [Anonymous Symbol]
    xunji.o(.ARM.exidx.text.DL_GPIO_readPins) refers to xunji.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    xunji.o(.text.xunji) refers to xunji.o(.text.my_GPIO_readPin) for my_GPIO_readPin
    xunji.o(.text.xunji) refers to fflti.o(.text) for __aeabi_i2f
    xunji.o(.text.xunji) refers to pid.o(.text.PID_Update) for PID_Update
    xunji.o(.text.xunji) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    xunji.o(.text.xunji) refers to ffixi.o(.text) for __aeabi_f2iz
    xunji.o(.text.xunji) refers to pwm.o(.text.AO_Control) for AO_Control
    xunji.o(.text.xunji) refers to pwm.o(.text.BO_Control) for BO_Control
    xunji.o(.text.xunji) refers to xunji.o(.bss.xunji_way) for xunji_way
    xunji.o(.text.xunji) refers to xunji.o(.bss.Angle_xunji_zuo) for Angle_xunji_zuo
    xunji.o(.text.xunji) refers to xunji.o(.bss.Angle_xunji_you) for Angle_xunji_you
    xunji.o(.text.xunji) refers to xunji.o(.data.AnglePID_xunji_zuo) for AnglePID_xunji_zuo
    xunji.o(.text.xunji) refers to xunji.o(.data.AnglePID_xunji_you) for AnglePID_xunji_you
    xunji.o(.ARM.exidx.text.xunji) refers to xunji.o(.text.xunji) for [Anonymous Symbol]
    zdt.o(.ARM.exidx.text.My_ABS) refers to zdt.o(.text.My_ABS) for [Anonymous Symbol]
    zdt.o(.text.Stop_Flag_Clear) refers to zdt.o(.bss.Motor01_Ready) for Motor01_Ready
    zdt.o(.text.Stop_Flag_Clear) refers to zdt.o(.bss.Motor02_Ready) for Motor02_Ready
    zdt.o(.text.Stop_Flag_Clear) refers to zdt.o(.bss.Stop_Flag_Car) for Stop_Flag_Car
    zdt.o(.ARM.exidx.text.Stop_Flag_Clear) refers to zdt.o(.text.Stop_Flag_Clear) for [Anonymous Symbol]
    zdt.o(.text.Motor_Receive_Data) refers to zdt.o(.bss.Motor_Receive_Data.RxState) for [Anonymous Symbol]
    zdt.o(.text.Motor_Receive_Data) refers to zdt.o(.bss.Motor_Receive_Data.RxCounter1) for [Anonymous Symbol]
    zdt.o(.text.Motor_Receive_Data) refers to zdt.o(.bss.RxBuffer2) for RxBuffer2
    zdt.o(.text.Motor_Receive_Data) refers to zdt.o(.bss.Stop_Flag_Car) for Stop_Flag_Car
    zdt.o(.text.Motor_Receive_Data) refers to zdt.o(.bss.Motor02_Ready) for Motor02_Ready
    zdt.o(.text.Motor_Receive_Data) refers to zdt.o(.bss.Motor01_Ready) for Motor01_Ready
    zdt.o(.ARM.exidx.text.Motor_Receive_Data) refers to zdt.o(.text.Motor_Receive_Data) for [Anonymous Symbol]
    zdt.o(.text.Motor_0) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_0) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.ARM.exidx.text.Motor_0) refers to zdt.o(.text.Motor_0) for [Anonymous Symbol]
    zdt.o(.text.Motor_set0) refers to zdt.o(.text.My_ABS) for My_ABS
    zdt.o(.text.Motor_set0) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_set0) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.ARM.exidx.text.Motor_set0) refers to zdt.o(.text.Motor_set0) for [Anonymous Symbol]
    zdt.o(.text.Motor_Enable) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_Enable) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.ARM.exidx.text.Motor_Enable) refers to zdt.o(.text.Motor_Enable) for [Anonymous Symbol]
    zdt.o(.text.Motor_Disable) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_Disable) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.ARM.exidx.text.Motor_Disable) refers to zdt.o(.text.Motor_Disable) for [Anonymous Symbol]
    zdt.o(.text.Motor_Reset) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_Reset) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.ARM.exidx.text.Motor_Reset) refers to zdt.o(.text.Motor_Reset) for [Anonymous Symbol]
    zdt.o(.text.Motor_Clear) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_Clear) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.ARM.exidx.text.Motor_Clear) refers to zdt.o(.text.Motor_Clear) for [Anonymous Symbol]
    zdt.o(.text.Motor_Init) refers to zdt.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    zdt.o(.text.Motor_Init) refers to zdt.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    zdt.o(.text.Motor_Init) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.text.Motor_Init) refers to zdt.o(.text.Stop_Flag_Clear) for Stop_Flag_Clear
    zdt.o(.text.Motor_Init) refers to zdt.o(.text.Motor_Enable_All) for Motor_Enable_All
    zdt.o(.text.Motor_Init) refers to zdt.o(.text.Motor_0) for Motor_0
    zdt.o(.ARM.exidx.text.Motor_Init) refers to zdt.o(.text.Motor_Init) for [Anonymous Symbol]
    zdt.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to zdt.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    zdt.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to zdt.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    zdt.o(.text.Motor_Enable_All) refers to zdt.o(.text.Motor_Enable) for Motor_Enable
    zdt.o(.ARM.exidx.text.Motor_Enable_All) refers to zdt.o(.text.Motor_Enable_All) for [Anonymous Symbol]
    zdt.o(.text.Motor_Disable_All) refers to zdt.o(.text.Motor_Disable) for Motor_Disable
    zdt.o(.ARM.exidx.text.Motor_Disable_All) refers to zdt.o(.text.Motor_Disable_All) for [Anonymous Symbol]
    zdt.o(.text.Motor_Set_PID) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_Set_PID) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.ARM.exidx.text.Motor_Set_PID) refers to zdt.o(.text.Motor_Set_PID) for [Anonymous Symbol]
    zdt.o(.text.Motor_Read_Current) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_Read_Current) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.ARM.exidx.text.Motor_Read_Current) refers to zdt.o(.text.Motor_Read_Current) for [Anonymous Symbol]
    zdt.o(.text.Motor_Run) refers to zdt.o(.text.Stop_Flag_Clear) for Stop_Flag_Clear
    zdt.o(.text.Motor_Run) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_Run) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.ARM.exidx.text.Motor_Run) refers to zdt.o(.text.Motor_Run) for [Anonymous Symbol]
    zdt.o(.text.Motor_SetSpeed) refers to zdt.o(.text.My_ABS) for My_ABS
    zdt.o(.text.Motor_SetSpeed) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_SetSpeed) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.ARM.exidx.text.Motor_SetSpeed) refers to zdt.o(.text.Motor_SetSpeed) for [Anonymous Symbol]
    zdt.o(.text.Motor_SetPosition) refers to zdt.o(.text.My_ABS) for My_ABS
    zdt.o(.text.Motor_SetPosition) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_SetPosition) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.text.Motor_SetPosition) refers to zdt.o(.bss.Stop_Flag_Car) for Stop_Flag_Car
    zdt.o(.ARM.exidx.text.Motor_SetPosition) refers to zdt.o(.text.Motor_SetPosition) for [Anonymous Symbol]
    zdt.o(.text.Motor_SetPosition_A) refers to zdt.o(.text.My_ABS) for My_ABS
    zdt.o(.text.Motor_SetPosition_A) refers to board.o(.text.UART_sendArray) for UART_sendArray
    zdt.o(.text.Motor_SetPosition_A) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.text.Motor_SetPosition_A) refers to zdt.o(.bss.Stop_Flag_Car) for Stop_Flag_Car
    zdt.o(.text.Motor_SetPosition_A) refers to zdt.o(.bss.RxBuffer2) for RxBuffer2
    zdt.o(.ARM.exidx.text.Motor_SetPosition_A) refers to zdt.o(.text.Motor_SetPosition_A) for [Anonymous Symbol]
    zdt.o(.text.Car_Go) refers to zdt.o(.text.Motor_SetPosition) for Motor_SetPosition
    zdt.o(.text.Car_Go) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    zdt.o(.text.Car_Go) refers to zdt.o(.text.My_ABS) for My_ABS
    zdt.o(.text.Car_Go) refers to zdt.o(.text.Motor_Run) for Motor_Run
    zdt.o(.text.Car_Go) refers to board.o(.text.delay_ms) for delay_ms
    zdt.o(.ARM.exidx.text.Car_Go) refers to zdt.o(.text.Car_Go) for [Anonymous Symbol]
    zdt.o(.text.Car_Go_Target) refers to zdt.o(.text.My_ABS) for My_ABS
    zdt.o(.text.Car_Go_Target) refers to dflti.o(.text) for __aeabi_ui2d
    zdt.o(.text.Car_Go_Target) refers to sqrt.o(i.sqrt) for sqrt
    zdt.o(.text.Car_Go_Target) refers to dmul.o(.text) for __aeabi_dmul
    zdt.o(.text.Car_Go_Target) refers to ddiv.o(.text) for __aeabi_ddiv
    zdt.o(.text.Car_Go_Target) refers to dfixi.o(.text) for __aeabi_d2iz
    zdt.o(.text.Car_Go_Target) refers to zdt.o(.text.Motor_SetPosition) for Motor_SetPosition
    zdt.o(.text.Car_Go_Target) refers to zdt.o(.text.Motor_Run) for Motor_Run
    zdt.o(.ARM.exidx.text.Car_Go_Target) refers to zdt.o(.text.Car_Go_Target) for [Anonymous Symbol]
    zdt.o(.text.Car_Go_Target_A) refers to zdt.o(.text.My_ABS) for My_ABS
    zdt.o(.text.Car_Go_Target_A) refers to dflti.o(.text) for __aeabi_ui2d
    zdt.o(.text.Car_Go_Target_A) refers to sqrt.o(i.sqrt) for sqrt
    zdt.o(.text.Car_Go_Target_A) refers to dmul.o(.text) for __aeabi_dmul
    zdt.o(.text.Car_Go_Target_A) refers to ddiv.o(.text) for __aeabi_ddiv
    zdt.o(.text.Car_Go_Target_A) refers to dfixi.o(.text) for __aeabi_d2iz
    zdt.o(.text.Car_Go_Target_A) refers to zdt.o(.text.Motor_SetPosition_A) for Motor_SetPosition_A
    zdt.o(.text.Car_Go_Target_A) refers to zdt.o(.text.Motor_Run) for Motor_Run
    zdt.o(.text.Car_Go_Target_A) refers to zdt.o(.bss.Car_Go_Target_A.Car_Now_X) for [Anonymous Symbol]
    zdt.o(.text.Car_Go_Target_A) refers to zdt.o(.bss.Car_Go_Target_A.Car_Now_Y) for [Anonymous Symbol]
    zdt.o(.ARM.exidx.text.Car_Go_Target_A) refers to zdt.o(.text.Car_Go_Target_A) for [Anonymous Symbol]
    zdt.o(.text.Car_Clear) refers to zdt.o(.text.Motor_Clear) for Motor_Clear
    zdt.o(.ARM.exidx.text.Car_Clear) refers to zdt.o(.text.Car_Clear) for [Anonymous Symbol]
    board.o(.text.board_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    board.o(.text.board_init) refers to board.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    board.o(.text.board_init) refers to board.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    board.o(.ARM.exidx.text.board_init) refers to board.o(.text.board_init) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to board.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to board.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.delay_us) refers to board.o(.text.delay_us) for [Anonymous Symbol]
    board.o(.text.delay_ms) refers to board.o(.text.delay_us) for delay_us
    board.o(.ARM.exidx.text.delay_ms) refers to board.o(.text.delay_ms) for [Anonymous Symbol]
    board.o(.text.delay_1us) refers to board.o(.text.delay_us) for delay_us
    board.o(.ARM.exidx.text.delay_1us) refers to board.o(.text.delay_1us) for [Anonymous Symbol]
    board.o(.text.delay_1ms) refers to board.o(.text.delay_ms) for delay_ms
    board.o(.ARM.exidx.text.delay_1ms) refers to board.o(.text.delay_1ms) for [Anonymous Symbol]
    board.o(.text.uart0_send_char) refers to board.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    board.o(.text.uart0_send_char) refers to board.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    board.o(.ARM.exidx.text.uart0_send_char) refers to board.o(.text.uart0_send_char) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.DL_UART_isBusy) refers to board.o(.text.DL_UART_isBusy) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.DL_UART_transmitData) refers to board.o(.text.DL_UART_transmitData) for [Anonymous Symbol]
    board.o(.text.uart0_send_string) refers to board.o(.text.uart0_send_char) for uart0_send_char
    board.o(.ARM.exidx.text.uart0_send_string) refers to board.o(.text.uart0_send_string) for [Anonymous Symbol]
    board.o(.text.UART_sendArray) refers to board.o(.text.DL_UART_isTXFIFOFull) for DL_UART_isTXFIFOFull
    board.o(.text.UART_sendArray) refers to board.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    board.o(.text.UART_sendArray) refers to board.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    board.o(.ARM.exidx.text.UART_sendArray) refers to board.o(.text.UART_sendArray) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.DL_UART_isTXFIFOFull) refers to board.o(.text.DL_UART_isTXFIFOFull) for [Anonymous Symbol]
    board.o(.ARM.exidx.text._sys_exit) refers to board.o(.text._sys_exit) for [Anonymous Symbol]
    board.o(.text.fputc) refers to board.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    board.o(.text.fputc) refers to board.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    board.o(.ARM.exidx.text.fputc) refers to board.o(.text.fputc) for [Anonymous Symbol]
    board.o(.text.UART0_IRQHandler) refers to board.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    board.o(.text.UART0_IRQHandler) refers to board.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    board.o(.text.UART0_IRQHandler) refers to board.o(.text.uart0_send_char) for uart0_send_char
    board.o(.text.UART0_IRQHandler) refers to board.o(.bss.recv0_length) for recv0_length
    board.o(.text.UART0_IRQHandler) refers to board.o(.bss.recv0_buff) for recv0_buff
    board.o(.text.UART0_IRQHandler) refers to board.o(.bss.recv0_flag) for recv0_flag
    board.o(.ARM.exidx.text.UART0_IRQHandler) refers to board.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.DL_UART_getPendingInterrupt) refers to board.o(.text.DL_UART_getPendingInterrupt) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.DL_UART_receiveData) refers to board.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    board.o(.text.UART2_IRQHandler) refers to board.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    board.o(.text.UART2_IRQHandler) refers to board.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    board.o(.text.UART2_IRQHandler) refers to zdt.o(.text.Motor_Receive_Data) for Motor_Receive_Data
    board.o(.ARM.exidx.text.UART2_IRQHandler) refers to board.o(.text.UART2_IRQHandler) for [Anonymous Symbol]
    hw_lcd.o(.text.spi_write_bus) refers to hw_lcd.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    hw_lcd.o(.text.spi_write_bus) refers to hw_lcd.o(.text.DL_SPI_isBusy) for DL_SPI_isBusy
    hw_lcd.o(.ARM.exidx.text.spi_write_bus) refers to hw_lcd.o(.text.spi_write_bus) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_SPI_transmitData8) refers to hw_lcd.o(.text.DL_SPI_transmitData8) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_SPI_isBusy) refers to hw_lcd.o(.text.DL_SPI_isBusy) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.spi_write_bus) for spi_write_bus
    hw_lcd.o(.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.ARM.exidx.text.LCD_Writ_Bus) refers to hw_lcd.o(.text.LCD_Writ_Bus) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.DL_GPIO_setPins) refers to hw_lcd.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_DATA8) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA8) refers to hw_lcd.o(.text.LCD_WR_DATA8) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_DATA) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA) refers to hw_lcd.o(.text.LCD_WR_DATA) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    hw_lcd.o(.text.LCD_WR_REG) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.ARM.exidx.text.LCD_WR_REG) refers to hw_lcd.o(.text.LCD_WR_REG) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_WR_REG) for LCD_WR_REG
    hw_lcd.o(.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_Address_Set) refers to hw_lcd.o(.text.LCD_Address_Set) for [Anonymous Symbol]
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_lcd.o(.text.lcd_init) refers to board.o(.text.delay_ms) for delay_ms
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.LCD_WR_REG) for LCD_WR_REG
    hw_lcd.o(.text.lcd_init) refers to hw_lcd.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    hw_lcd.o(.ARM.exidx.text.lcd_init) refers to hw_lcd.o(.text.lcd_init) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_Fill) refers to hw_lcd.o(.text.LCD_Fill) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_DrawPoint) refers to hw_lcd.o(.text.LCD_DrawPoint) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawLine) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.LCD_DrawLine) refers to hw_lcd.o(.text.LCD_DrawLine) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.ARM.exidx.text.LCD_DrawVerrticalLine) refers to hw_lcd.o(.text.LCD_DrawVerrticalLine) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_DrawRectangle) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    hw_lcd.o(.ARM.exidx.text.LCD_DrawRectangle) refers to hw_lcd.o(.text.LCD_DrawRectangle) for [Anonymous Symbol]
    hw_lcd.o(.text.Draw_Circle) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.Draw_Circle) refers to hw_lcd.o(.text.Draw_Circle) for [Anonymous Symbol]
    hw_lcd.o(.text.Drawarc) refers to dflti.o(.text) for __aeabi_ui2d
    hw_lcd.o(.text.Drawarc) refers to sqrt.o(i.sqrt) for sqrt
    hw_lcd.o(.text.Drawarc) refers to dfixi.o(.text) for __aeabi_d2iz
    hw_lcd.o(.text.Drawarc) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.ARM.exidx.text.Drawarc) refers to hw_lcd.o(.text.Drawarc) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.Drawarc) for Drawarc
    hw_lcd.o(.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_Fill) for LCD_Fill
    hw_lcd.o(.ARM.exidx.text.LCD_ArcRect) refers to hw_lcd.o(.text.LCD_ArcRect) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese12x12) for LCD_ShowChinese12x12
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese16x16) for LCD_ShowChinese16x16
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese24x24) for LCD_ShowChinese24x24
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese32x32) for LCD_ShowChinese32x32
    hw_lcd.o(.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese) refers to hw_lcd.o(.text.LCD_ShowChinese) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.rodata.tfont12) for tfont12
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese12x12) refers to hw_lcd.o(.text.LCD_ShowChinese12x12) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.rodata.tfont16) for tfont16
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese16x16) refers to hw_lcd.o(.text.LCD_ShowChinese16x16) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.rodata.tfont24) for tfont24
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese24x24) refers to hw_lcd.o(.text.LCD_ShowChinese24x24) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.rodata.tfont32) for tfont32
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese32x32) refers to hw_lcd.o(.text.LCD_ShowChinese32x32) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    hw_lcd.o(.text.LCD_ShowChar) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_3216) for ascii_3216
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_2412) for ascii_2412
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_1608) for ascii_1608
    hw_lcd.o(.text.LCD_ShowChar) refers to hw_lcd.o(.rodata.ascii_1206) for ascii_1206
    hw_lcd.o(.ARM.exidx.text.LCD_ShowChar) refers to hw_lcd.o(.text.LCD_ShowChar) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowString) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowString) refers to hw_lcd.o(.text.LCD_ShowString) for [Anonymous Symbol]
    hw_lcd.o(.ARM.exidx.text.mypow) refers to hw_lcd.o(.text.mypow) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.mypow) for mypow
    hw_lcd.o(.text.LCD_ShowIntNum) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    hw_lcd.o(.text.LCD_ShowIntNum) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowIntNum) refers to hw_lcd.o(.text.LCD_ShowIntNum) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to ffixui.o(.text) for __aeabi_f2uiz
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.mypow) for mypow
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    hw_lcd.o(.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.LCD_ShowChar) for LCD_ShowChar
    hw_lcd.o(.ARM.exidx.text.LCD_ShowFloatNum1) refers to hw_lcd.o(.text.LCD_ShowFloatNum1) for [Anonymous Symbol]
    hw_lcd.o(.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_Address_Set) for LCD_Address_Set
    hw_lcd.o(.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    hw_lcd.o(.ARM.exidx.text.LCD_ShowPicture) refers to hw_lcd.o(.text.LCD_ShowPicture) for [Anonymous Symbol]
    key.o(.text.Key_Read) refers to xunji.o(.text.my_GPIO_readPin) for my_GPIO_readPin
    key.o(.text.Key_Read) refers to board.o(.text.delay_ms) for delay_ms
    key.o(.text.Key_Read) refers to key.o(.data.keys) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.Key_Read) refers to key.o(.text.Key_Read) for [Anonymous Symbol]
    key.o(.text.Key_ReadAll) refers to key.o(.text.Key_Read) for Key_Read
    key.o(.ARM.exidx.text.Key_ReadAll) refers to key.o(.text.Key_ReadAll) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.IIC_Start) refers to bsp_mpu6050.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    bsp_mpu6050.o(.text.IIC_Start) refers to bsp_mpu6050.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_mpu6050.o(.text.IIC_Start) refers to bsp_mpu6050.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    bsp_mpu6050.o(.text.IIC_Start) refers to bsp_mpu6050.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    bsp_mpu6050.o(.text.IIC_Start) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Start) refers to bsp_mpu6050.o(.text.IIC_Start) for [Anonymous Symbol]
    bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to bsp_mpu6050.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_setPins) refers to bsp_mpu6050.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to bsp_mpu6050.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to bsp_mpu6050.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.IIC_Stop) refers to bsp_mpu6050.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    bsp_mpu6050.o(.text.IIC_Stop) refers to bsp_mpu6050.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_mpu6050.o(.text.IIC_Stop) refers to bsp_mpu6050.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    bsp_mpu6050.o(.text.IIC_Stop) refers to bsp_mpu6050.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    bsp_mpu6050.o(.text.IIC_Stop) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop) refers to bsp_mpu6050.o(.text.IIC_Stop) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers to bsp_mpu6050.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers to bsp_mpu6050.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers to bsp_mpu6050.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers to bsp_mpu6050.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    bsp_mpu6050.o(.text.IIC_Send_Ack) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack) refers to bsp_mpu6050.o(.text.IIC_Send_Ack) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.I2C_WaitAck) refers to bsp_mpu6050.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    bsp_mpu6050.o(.text.I2C_WaitAck) refers to bsp_mpu6050.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_mpu6050.o(.text.I2C_WaitAck) refers to bsp_mpu6050.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    bsp_mpu6050.o(.text.I2C_WaitAck) refers to bsp_mpu6050.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    bsp_mpu6050.o(.text.I2C_WaitAck) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.text.I2C_WaitAck) refers to bsp_mpu6050.o(.text.IIC_Stop) for IIC_Stop
    bsp_mpu6050.o(.text.I2C_WaitAck) refers to bsp_mpu6050.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    bsp_mpu6050.o(.text.I2C_WaitAck) refers to bsp_mpu6050.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck) refers to bsp_mpu6050.o(.text.I2C_WaitAck) for [Anonymous Symbol]
    bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to bsp_mpu6050.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_readPins) refers to bsp_mpu6050.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.Send_Byte) refers to bsp_mpu6050.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    bsp_mpu6050.o(.text.Send_Byte) refers to bsp_mpu6050.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_mpu6050.o(.text.Send_Byte) refers to bsp_mpu6050.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    bsp_mpu6050.o(.text.Send_Byte) refers to bsp_mpu6050.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    bsp_mpu6050.o(.text.Send_Byte) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.ARM.exidx.text.Send_Byte) refers to bsp_mpu6050.o(.text.Send_Byte) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.Read_Byte) refers to bsp_mpu6050.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    bsp_mpu6050.o(.text.Read_Byte) refers to bsp_mpu6050.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    bsp_mpu6050.o(.text.Read_Byte) refers to board.o(.text.delay_us) for delay_us
    bsp_mpu6050.o(.text.Read_Byte) refers to bsp_mpu6050.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_mpu6050.o(.text.Read_Byte) refers to bsp_mpu6050.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    bsp_mpu6050.o(.ARM.exidx.text.Read_Byte) refers to bsp_mpu6050.o(.text.Read_Byte) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers to bsp_mpu6050.o(.text.IIC_Start) for IIC_Start
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers to bsp_mpu6050.o(.text.Send_Byte) for Send_Byte
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers to bsp_mpu6050.o(.text.I2C_WaitAck) for I2C_WaitAck
    bsp_mpu6050.o(.text.MPU6050_WriteReg) refers to bsp_mpu6050.o(.text.IIC_Stop) for IIC_Stop
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.IIC_Start) for IIC_Start
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.Send_Byte) for Send_Byte
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.I2C_WaitAck) for I2C_WaitAck
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.IIC_Stop) for IIC_Stop
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.Read_Byte) for Read_Byte
    bsp_mpu6050.o(.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.IIC_Send_Ack) for IIC_Send_Ack
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr) refers to bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr) refers to bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_LPF) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF) refers to bsp_mpu6050.o(.text.MPU_Set_LPF) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.text.MPU_Set_Rate) refers to bsp_mpu6050.o(.text.MPU_Set_LPF) for MPU_Set_LPF
    bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate) refers to bsp_mpu6050.o(.text.MPU_Set_Rate) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050ReadGyro) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro) refers to bsp_mpu6050.o(.text.MPU6050ReadGyro) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050ReadAcc) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc) refers to bsp_mpu6050.o(.text.MPU6050ReadAcc) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to dflti.o(.text) for __aeabi_i2d
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to ddiv.o(.text) for __aeabi_ddiv
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to daddsub.o(.text) for __aeabi_dadd
    bsp_mpu6050.o(.text.MPU6050_GetTemp) refers to d2f.o(.text) for __aeabi_d2f
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp) refers to bsp_mpu6050.o(.text.MPU6050_GetTemp) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050ReadID) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    bsp_mpu6050.o(.text.MPU6050ReadID) refers to printf.o(.text) for printf
    bsp_mpu6050.o(.text.MPU6050ReadID) refers to bsp_mpu6050.o(.rodata.str1.1) for [Anonymous Symbol]
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID) refers to bsp_mpu6050.o(.text.MPU6050ReadID) for [Anonymous Symbol]
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    bsp_mpu6050.o(.text.MPU6050_Init) refers to board.o(.text.delay_ms) for delay_ms
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr) for MPU_Set_Gyro_Fsr
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr) for MPU_Set_Accel_Fsr
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU_Set_Rate) for MPU_Set_Rate
    bsp_mpu6050.o(.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU6050ReadID) for MPU6050ReadID
    bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers to bsp_mpu6050.o(.text.MPU6050_Init) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_reg_dump) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_reg_dump) refers to printf.o(.text) for printf
    inv_mpu.o(.text.mpu_reg_dump) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_reg_dump) refers to inv_mpu.o(.rodata.str1.1) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_reg_dump) refers to inv_mpu.o(.text.mpu_reg_dump) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_reg) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_reg) refers to inv_mpu.o(.text.mpu_read_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_init) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_init) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_init) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_init) refers to printf.o(.text) for printf
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_init) refers to inv_mpu.o(.rodata.str1.1) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_init) refers to inv_mpu.o(.text.mpu_init) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_gyro_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr) refers to inv_mpu.o(.text.mpu_set_gyro_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_accel_fsr) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_accel_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr) refers to inv_mpu.o(.text.mpu_set_accel_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_lpf) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_lpf) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_lpf) refers to inv_mpu.o(.text.mpu_set_lpf) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_sample_rate) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(.text.mpu_set_sample_rate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_set_sample_rate) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_sample_rate) refers to inv_mpu.o(.text.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(.text.mpu_set_sample_rate) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate) refers to inv_mpu.o(.text.mpu_set_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_configure_fifo) refers to inv_mpu.o(.text.set_int_enable) for set_int_enable
    inv_mpu.o(.text.mpu_configure_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_configure_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo) refers to inv_mpu.o(.text.mpu_configure_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_bypass) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_set_bypass) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_bypass) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_set_bypass) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_bypass) refers to inv_mpu.o(.text.mpu_set_bypass) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_sensors) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_sensors) refers to inv_mpu.o(.text.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(.text.mpu_set_sensors) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_set_sensors) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_sensors) refers to inv_mpu.o(.text.mpu_set_sensors) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to inv_mpu.o(.text.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to inv_mpu.o(.text.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_lp_accel_mode) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_int_latched) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_int_latched) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched) refers to inv_mpu.o(.text.mpu_set_int_latched) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_reg) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_gyro_reg) refers to inv_mpu.o(.text.mget_ms) for mget_ms
    inv_mpu.o(.text.mpu_get_gyro_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg) refers to inv_mpu.o(.text.mpu_get_gyro_reg) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mget_ms) refers to inv_mpu.o(.text.mget_ms) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_reg) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_accel_reg) refers to inv_mpu.o(.text.mget_ms) for mget_ms
    inv_mpu.o(.text.mpu_get_accel_reg) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg) refers to inv_mpu.o(.text.mpu_get_accel_reg) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_temperature) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_temperature) refers to inv_mpu.o(.text.mget_ms) for mget_ms
    inv_mpu.o(.text.mpu_get_temperature) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.mpu_get_temperature) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(.text.mpu_get_temperature) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(.text.mpu_get_temperature) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(.text.mpu_get_temperature) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.mpu_get_temperature) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu.o(.text.mpu_get_temperature) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_temperature) refers to inv_mpu.o(.text.mpu_get_temperature) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_accel_bias) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_set_accel_bias) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    inv_mpu.o(.text.mpu_set_accel_bias) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_accel_bias) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias) refers to inv_mpu.o(.text.mpu_set_accel_bias) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_reset_fifo) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_reset_fifo) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_reset_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr) refers to inv_mpu.o(.text.mpu_get_gyro_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_fsr) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr) refers to inv_mpu.o(.text.mpu_get_accel_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_lpf) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_lpf) refers to inv_mpu.o(.text.mpu_get_lpf) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_sample_rate) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate) refers to inv_mpu.o(.text.mpu_get_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate) refers to inv_mpu.o(.text.mpu_get_compass_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate) refers to inv_mpu.o(.text.mpu_set_compass_sample_rate) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_gyro_sens) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens) refers to inv_mpu.o(.text.mpu_get_gyro_sens) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_accel_sens) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens) refers to inv_mpu.o(.text.mpu_get_accel_sens) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_fifo_config) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config) refers to inv_mpu.o(.text.mpu_get_fifo_config) for [Anonymous Symbol]
    inv_mpu.o(.text.set_int_enable) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.set_int_enable) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.set_int_enable) refers to inv_mpu.o(.text.set_int_enable) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_power_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_power_state) refers to inv_mpu.o(.text.mpu_get_power_state) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_int_status) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_get_int_status) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_int_status) refers to inv_mpu.o(.text.mpu_get_int_status) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_fifo) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_read_fifo) refers to inv_mpu.o(.text.mget_ms) for mget_ms
    inv_mpu.o(.text.mpu_read_fifo) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_read_fifo) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo) refers to inv_mpu.o(.text.mpu_read_fifo) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu.o(.text.mpu_read_fifo_stream) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream) refers to inv_mpu.o(.text.mpu_read_fifo_stream) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_int_level) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_int_level) refers to inv_mpu.o(.text.mpu_set_int_level) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_get_fifo_config) for mpu_get_fifo_config
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.get_st_biases) for get_st_biases
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.accel_self_test) for accel_self_test
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.gyro_self_test) for gyro_self_test
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_run_self_test) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_run_self_test) refers to inv_mpu.o(.text.mpu_run_self_test) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.set_int_enable) for set_int_enable
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_set_dmp_state) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(.text.mpu_set_dmp_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state) refers to inv_mpu.o(.text.mpu_set_dmp_state) for [Anonymous Symbol]
    inv_mpu.o(.text.get_st_biases) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.get_st_biases) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.get_st_biases) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.get_st_biases) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu.o(.text.get_st_biases) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.get_st_biases) refers to llsdiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(.ARM.exidx.text.get_st_biases) refers to inv_mpu.o(.text.get_st_biases) for [Anonymous Symbol]
    inv_mpu.o(.text.accel_self_test) refers to inv_mpu.o(.text.get_accel_prod_shift) for get_accel_prod_shift
    inv_mpu.o(.text.accel_self_test) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.accel_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.accel_self_test) refers to fcmp.o(i._feq) for __aeabi_fcmpeq
    inv_mpu.o(.text.accel_self_test) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(.text.accel_self_test) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(.text.accel_self_test) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    inv_mpu.o(.text.accel_self_test) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    inv_mpu.o(.text.accel_self_test) refers to inv_mpu.o(.rodata.test) for test
    inv_mpu.o(.ARM.exidx.text.accel_self_test) refers to inv_mpu.o(.text.accel_self_test) for [Anonymous Symbol]
    inv_mpu.o(.text.gyro_self_test) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.gyro_self_test) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.gyro_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.gyro_self_test) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(.text.gyro_self_test) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(.text.gyro_self_test) refers to fcmp.o(i._fleq) for __aeabi_fcmple
    inv_mpu.o(.text.gyro_self_test) refers to fcmp.o(i._fls) for __aeabi_fcmplt
    inv_mpu.o(.text.gyro_self_test) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.text.gyro_self_test) refers to inv_mpu.o(.rodata.test) for test
    inv_mpu.o(.ARM.exidx.text.gyro_self_test) refers to inv_mpu.o(.text.gyro_self_test) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_write_mem) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_write_mem) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_write_mem) refers to inv_mpu.o(.text.mpu_write_mem) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_read_mem) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_read_mem) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.mpu_read_mem) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_read_mem) refers to inv_mpu.o(.text.mpu_read_mem) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_load_firmware) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu.o(.text.mpu_load_firmware) refers to inv_mpu.o(.text.mpu_read_mem) for mpu_read_mem
    inv_mpu.o(.text.mpu_load_firmware) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(.text.mpu_load_firmware) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_load_firmware) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_load_firmware) refers to inv_mpu.o(.text.mpu_load_firmware) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_get_dmp_state) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state) refers to inv_mpu.o(.text.mpu_get_dmp_state) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.setup_compass) refers to inv_mpu.o(.text.setup_compass) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg) refers to inv_mpu.o(.text.mpu_get_compass_reg) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr) refers to inv_mpu.o(.text.mpu_get_compass_fsr) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_get_fifo_config) for mpu_get_fifo_config
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.set_int_enable) for set_int_enable
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to bsp_mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to board.o(.text.delay_ms) for delay_ms
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt) refers to inv_mpu.o(.text.mpu_lp_motion_interrupt) for [Anonymous Symbol]
    inv_mpu.o(.text.run_self_test) refers to inv_mpu.o(.text.mpu_run_self_test) for mpu_run_self_test
    inv_mpu.o(.text.run_self_test) refers to inv_mpu.o(.text.mpu_get_gyro_sens) for mpu_get_gyro_sens
    inv_mpu.o(.text.run_self_test) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.run_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.run_self_test) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu.o(.text.run_self_test) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) for dmp_set_gyro_bias
    inv_mpu.o(.text.run_self_test) refers to inv_mpu.o(.text.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu.o(.text.run_self_test) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) for dmp_set_accel_bias
    inv_mpu.o(.ARM.exidx.text.run_self_test) refers to inv_mpu.o(.text.run_self_test) for [Anonymous Symbol]
    inv_mpu.o(.text.inv_orientation_matrix_to_scalar) refers to inv_mpu.o(.text.inv_row_2_scale) for inv_row_2_scale
    inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar) refers to inv_mpu.o(.text.inv_orientation_matrix_to_scalar) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.inv_row_2_scale) refers to inv_mpu.o(.text.inv_row_2_scale) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_init) for mpu_init
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) for dmp_load_motion_driver_firmware
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.inv_orientation_matrix_to_scalar) for inv_orientation_matrix_to_scalar
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) for dmp_set_orientation
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) for dmp_enable_feature
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) for dmp_set_fifo_rate
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(.text.mpu_dmp_init) refers to inv_mpu.o(.data.gyro_orientation) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_init) refers to inv_mpu.o(.text.mpu_dmp_init) for [Anonymous Symbol]
    inv_mpu.o(.text.mpu_dmp_get_data) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) for dmp_read_fifo
    inv_mpu.o(.text.mpu_dmp_get_data) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(.text.mpu_dmp_get_data) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.mpu_dmp_get_data) refers to faddsub.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(.text.mpu_dmp_get_data) refers to f2d.o(.text) for __aeabi_f2d
    inv_mpu.o(.text.mpu_dmp_get_data) refers to asin.o(i.asin) for asin
    inv_mpu.o(.text.mpu_dmp_get_data) refers to dmul.o(.text) for __aeabi_dmul
    inv_mpu.o(.text.mpu_dmp_get_data) refers to d2f.o(.text) for __aeabi_d2f
    inv_mpu.o(.text.mpu_dmp_get_data) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(.text.mpu_dmp_get_data) refers to atan2.o(i.atan2) for atan2
    inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data) refers to inv_mpu.o(.text.mpu_dmp_get_data) for [Anonymous Symbol]
    inv_mpu.o(.text.get_accel_prod_shift) refers to bsp_mpu6050.o(.text.MPU6050_ReadData) for MPU6050_ReadData
    inv_mpu.o(.text.get_accel_prod_shift) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(.text.get_accel_prod_shift) refers to inv_mpu.o(.data.st) for [Anonymous Symbol]
    inv_mpu.o(.ARM.exidx.text.get_accel_prod_shift) refers to inv_mpu.o(.text.get_accel_prod_shift) for [Anonymous Symbol]
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.reg) for reg
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.hw) for hw
    inv_mpu.o(.data.st) refers to inv_mpu.o(.rodata.test) for test
    inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) refers to inv_mpu.o(.text.mpu_load_firmware) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.rodata.dmp_memory) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to llmul.o(.text) for __aeabi_lmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu.o(.text.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to llmul.o(.text) for __aeabi_lmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to fflti.o(.text) for __aeabi_ui2f
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to inv_mpu.o(.text.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_thresh) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_axes) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time_multi) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_idiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_thresh) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_timeout) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count) refers to inv_mpu.o(.text.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_step_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_step_count) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time) refers to inv_mpu.o(.text.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_walk_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_walk_time) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal) for dmp_enable_gyro_cal
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh) for dmp_set_tap_thresh
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes) for dmp_set_tap_axes
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count) for dmp_set_tap_count
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time) for dmp_set_tap_time
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi) for dmp_set_tap_time_multi
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh) for dmp_set_shake_reject_thresh
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time) for dmp_set_shake_reject_time
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout) for dmp_set_shake_reject_timeout
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) for dmp_enable_lp_quat
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) for dmp_enable_6x_lp_quat
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_gyro_cal) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_lp_quat) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_6x_lp_quat) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode) refers to inv_mpu.o(.text.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_interrupt_mode) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.mpu_read_fifo_stream) for mpu_read_fifo_stream
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.text.decode_gesture) for decode_gesture
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu.o(.text.mget_ms) for mget_ms
    inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.decode_gesture) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.decode_gesture) refers to inv_mpu_dmp_motion_driver.o(.text.decode_gesture) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.bss.dmp) for [Anonymous Symbol]
    inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb) for [Anonymous Symbol]
    wit.o(.text.WIT_Init) refers to wit.o(.text.DL_DMA_setSrcAddr) for DL_DMA_setSrcAddr
    wit.o(.text.WIT_Init) refers to wit.o(.text.DL_DMA_setDestAddr) for DL_DMA_setDestAddr
    wit.o(.text.WIT_Init) refers to wit.o(.text.DL_DMA_setTransferSize) for DL_DMA_setTransferSize
    wit.o(.text.WIT_Init) refers to wit.o(.text.DL_DMA_enableChannel) for DL_DMA_enableChannel
    wit.o(.text.WIT_Init) refers to wit.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    wit.o(.text.WIT_Init) refers to wit.o(.bss.wit_dmaBuffer) for wit_dmaBuffer
    wit.o(.ARM.exidx.text.WIT_Init) refers to wit.o(.text.WIT_Init) for [Anonymous Symbol]
    wit.o(.ARM.exidx.text.DL_DMA_setSrcAddr) refers to wit.o(.text.DL_DMA_setSrcAddr) for [Anonymous Symbol]
    wit.o(.ARM.exidx.text.DL_DMA_setDestAddr) refers to wit.o(.text.DL_DMA_setDestAddr) for [Anonymous Symbol]
    wit.o(.ARM.exidx.text.DL_DMA_setTransferSize) refers to wit.o(.text.DL_DMA_setTransferSize) for [Anonymous Symbol]
    wit.o(.ARM.exidx.text.DL_DMA_enableChannel) refers to wit.o(.text.DL_DMA_enableChannel) for [Anonymous Symbol]
    wit.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to wit.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    wit.o(.text.UART3_IRQHandler) refers to wit.o(.text.DL_DMA_disableChannel) for DL_DMA_disableChannel
    wit.o(.text.UART3_IRQHandler) refers to wit.o(.text.DL_DMA_getTransferSize) for DL_DMA_getTransferSize
    wit.o(.text.UART3_IRQHandler) refers to wit.o(.text.DL_UART_isRXFIFOEmpty) for DL_UART_isRXFIFOEmpty
    wit.o(.text.UART3_IRQHandler) refers to wit.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    wit.o(.text.UART3_IRQHandler) refers to dflti.o(.text) for __aeabi_i2d
    wit.o(.text.UART3_IRQHandler) refers to ddiv.o(.text) for __aeabi_ddiv
    wit.o(.text.UART3_IRQHandler) refers to dfixi.o(.text) for __aeabi_d2iz
    wit.o(.text.UART3_IRQHandler) refers to d2f.o(.text) for __aeabi_d2f
    wit.o(.text.UART3_IRQHandler) refers to dmul.o(.text) for __aeabi_dmul
    wit.o(.text.UART3_IRQHandler) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for DL_UART_drainRXFIFO
    wit.o(.text.UART3_IRQHandler) refers to wit.o(.text.DL_DMA_setDestAddr) for DL_DMA_setDestAddr
    wit.o(.text.UART3_IRQHandler) refers to wit.o(.text.DL_DMA_setTransferSize) for DL_DMA_setTransferSize
    wit.o(.text.UART3_IRQHandler) refers to wit.o(.text.DL_DMA_enableChannel) for DL_DMA_enableChannel
    wit.o(.text.UART3_IRQHandler) refers to wit.o(.bss.wit_dmaBuffer) for wit_dmaBuffer
    wit.o(.text.UART3_IRQHandler) refers to wit.o(.bss.wit_data) for wit_data
    wit.o(.ARM.exidx.text.UART3_IRQHandler) refers to wit.o(.text.UART3_IRQHandler) for [Anonymous Symbol]
    wit.o(.ARM.exidx.text.DL_DMA_disableChannel) refers to wit.o(.text.DL_DMA_disableChannel) for [Anonymous Symbol]
    wit.o(.ARM.exidx.text.DL_DMA_getTransferSize) refers to wit.o(.text.DL_DMA_getTransferSize) for [Anonymous Symbol]
    wit.o(.ARM.exidx.text.DL_UART_isRXFIFOEmpty) refers to wit.o(.text.DL_UART_isRXFIFOEmpty) for [Anonymous Symbol]
    wit.o(.ARM.exidx.text.DL_UART_receiveData) refers to wit.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    llsdiv.o(.text) refers to lludiv.o(.text) for __aeabi_uldivmod
    printf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    printf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    printf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    printf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    printf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    printf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    printf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    printf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    printf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    printf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    printf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    printf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    printf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    printf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    printf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    printf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    printf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    printf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    printf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    printf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    printf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    printf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    printf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    printf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    printf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    printf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    printf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    printf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    printf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    printf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    printf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    printf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    printf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    printf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    printf.o(.text) refers to board.o(.bss.__stdout) for __stdout
    rand.o(.emb_text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000F) for __rt_lib_init_rand_2
    rand.o(.emb_text) refers to rand.o(.text) for _rand_init
    rand.o(.emb_text) refers to rand.o(.bss) for _random_number_data
    rand.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000F) for __rt_lib_init_rand_2
    rand.o(.text) refers to rand.o(.bss) for .bss
    rand.o(.bss) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000F) for __rt_lib_init_rand_2
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__eqdf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__eqdf2) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(i.__gedf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__gedf2) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i.__gtdf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__gtdf2) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i.__ledf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__ledf2) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i.__ltdf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__ltdf2) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i.__nedf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__nedf2) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(i._deq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._deq) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(i._dgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dgeq) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i._dgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dgr) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i._dleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dleq) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i._dls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dls) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i._dneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dneq) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    ddiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers to ddiv.o(.constdata) for .constdata
    ddiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixui.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fadd) for _fadd1
    faddsub.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fsub) refers to faddsub.o(x$fpl$fadd) for _fadd1
    fcmp.o(i.__eqsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__eqsf2) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i.__gesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__gesf2) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i.__gtsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__gtsf2) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i.__lesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__lesf2) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i.__ltsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__ltsf2) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i.__nesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__nesf2) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._feq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._feq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._fgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgeq) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgr) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fleq) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fls) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fneq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fdiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(.text) refers to fdiv.o(.constdata) for .constdata
    fdiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixui.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.__softfp_asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.asin) for asin
    asin.o(i.asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.asin) refers to _rserrno.o(.text) for __set_errno
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.asin) refers to daddsub.o(.text) for __aeabi_dadd
    asin.o(i.asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.asin) refers to dscalbn.o(.text) for __ARM_scalbn
    asin.o(i.asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.asin) refers to asin.o(.constdata) for .constdata
    asin.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub.o(.text) for __aeabi_dsub
    atan2.o(i.atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to board.o(.text.fputc) for fputc
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _c16rtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rand.o(.text) for _rand_init
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dgef.o(x$fpl$dgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dgef.o(x$fpl$dgeqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dlef.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dlef.o(x$fpl$dleqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dscalbn.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    fgef.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgef.o(x$fpl$fgeqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    flef.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flef.o(x$fpl$fleqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalbn.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalbn.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalbn.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalbn.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to empty.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(.text) for _btod_d2e
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _c16rtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    dcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    dcmpin.o(.text) refers to dnan2.o(.text) for __fpl_dcheck_NaN2
    fcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    fcmpin.o(.text) refers to fnan2.o(.text) for __fpl_fcheck_NaN2
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    bigflt0.o(.text) refers to btod.o(.text) for _btod_emul
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(.text) refers to btod.o(i.__ARM_common_ll_muluu) for __ARM_common_ll_muluu
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv6m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    cmpret.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    fnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    ieee_status.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to board.o(.text._sys_exit) for _sys_exit
    btod_accurate_common.o(.text) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv6m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to board.o(.text._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    retnan.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to board.o(.text._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.text.Read_Key), (60 bytes).
    Removing empty.o(.ARM.exidx.text.Read_Key), (8 bytes).
    Removing empty.o(.text.UiDispatcher), (276 bytes).
    Removing empty.o(.ARM.exidx.text.UiDispatcher), (8 bytes).
    Removing empty.o(.text.handle_ui_home_one), (8 bytes).
    Removing empty.o(.ARM.exidx.text.handle_ui_home_one), (8 bytes).
    Removing empty.o(.text.handle_ui_left_right_page), (8 bytes).
    Removing empty.o(.ARM.exidx.text.handle_ui_left_right_page), (8 bytes).
    Removing empty.o(.text.handle_ui_question_page), (8 bytes).
    Removing empty.o(.ARM.exidx.text.handle_ui_question_page), (8 bytes).
    Removing empty.o(.bss.key24_val), (4 bytes).
    Removing empty.o(.bss.key25_val), (4 bytes).
    Removing empty.o(.bss.key26_val), (4 bytes).
    Removing empty.o(.bss.key27_val), (4 bytes).
    Removing empty.o(.bss.last_key24_val), (4 bytes).
    Removing empty.o(.bss.last_key25_val), (4 bytes).
    Removing empty.o(.bss.last_key26_val), (4 bytes).
    Removing empty.o(.bss.last_key27_val), (4 bytes).
    Removing empty.o(.bss.Ui_Index), (4 bytes).
    Removing empty.o(.bss.Ui_Disp), (4 bytes).
    Removing empty.o(.rodata.MAX_UI_INDEX), (4 bytes).
    Removing empty.o(.bss.yaw), (4 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_TICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_WIT_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_2_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_LCD_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (104 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (104 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunctionFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableHiZ), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setAnalogGlitchFilterPulseWidth), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableAnalogGlitchFilter), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_resetControllerTransfer), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setTimerPeriod), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerTXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerRXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableControllerClockStretching), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableController), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableDMAReceiveEvent), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXInterruptTimeout), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setBitRateSerialClockDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_setFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SPI_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_WIT_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing app_ui.o(.text), (0 bytes).
    Removing app_ui.o(.text.disp_x_center_zero), (100 bytes).
    Removing app_ui.o(.ARM.exidx.text.disp_x_center_zero), (8 bytes).
    Removing app_ui.o(.text.disp_string_rect_zero), (136 bytes).
    Removing app_ui.o(.ARM.exidx.text.disp_string_rect_zero), (8 bytes).
    Removing app_ui.o(.text.disp_select_box_zero), (234 bytes).
    Removing app_ui.o(.ARM.exidx.text.disp_select_box_zero), (8 bytes).
    Removing app_ui.o(.text.draw_starfield), (400 bytes).
    Removing app_ui.o(.ARM.exidx.text.draw_starfield), (8 bytes).
    Removing app_ui.o(.text.ui_home_page), (52 bytes).
    Removing app_ui.o(.ARM.exidx.text.ui_home_page), (8 bytes).
    Removing app_ui.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing app_ui.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing app_ui.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing app_ui.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing app_ui.o(.text.star_expansion_fullscreen_animation), (960 bytes).
    Removing app_ui.o(.ARM.exidx.text.star_expansion_fullscreen_animation), (8 bytes).
    Removing app_ui.o(.text.ui_home_One), (276 bytes).
    Removing app_ui.o(.ARM.exidx.text.ui_home_One), (8 bytes).
    Removing app_ui.o(.text.ui_left_right_page), (568 bytes).
    Removing app_ui.o(.ARM.exidx.text.ui_left_right_page), (8 bytes).
    Removing app_ui.o(.text.ui_question_page), (1108 bytes).
    Removing app_ui.o(.ARM.exidx.text.ui_question_page), (8 bytes).
    Removing app_ui.o(.rodata.str1.1), (116 bytes).
    Removing app_ui.o(.bss.ui_question_page.current_index), (1 bytes).
    Removing encoder.o(.text), (0 bytes).
    Removing encoder.o(.text.encoder_init), (22 bytes).
    Removing encoder.o(.ARM.exidx.text.encoder_init), (8 bytes).
    Removing encoder.o(.text.__NVIC_EnableIRQ), (44 bytes).
    Removing encoder.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing encoder.o(.text.encoder_callback), (68 bytes).
    Removing encoder.o(.ARM.exidx.text.encoder_callback), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing encoder.o(.text.timer_init), (22 bytes).
    Removing encoder.o(.ARM.exidx.text.timer_init), (8 bytes).
    Removing encoder.o(.text.__NVIC_ClearPendingIRQ), (44 bytes).
    Removing encoder.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing encoder.o(.bss.left_speed), (2 bytes).
    Removing encoder.o(.bss.left_distance), (4 bytes).
    Removing encoder.o(.bss.right_speed), (2 bytes).
    Removing encoder.o(.bss.right_distance), (4 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.OLED_I2C_Init), (2 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_I2C_Init), (8 bytes).
    Removing oled.o(.text.I2C_WriteByte), (100 bytes).
    Removing oled.o(.ARM.exidx.text.I2C_WriteByte), (8 bytes).
    Removing oled.o(.text.DL_I2C_getControllerStatus), (20 bytes).
    Removing oled.o(.ARM.exidx.text.DL_I2C_getControllerStatus), (8 bytes).
    Removing oled.o(.text.DL_I2C_startControllerTransfer), (80 bytes).
    Removing oled.o(.ARM.exidx.text.DL_I2C_startControllerTransfer), (8 bytes).
    Removing oled.o(.text.WriteCmd), (22 bytes).
    Removing oled.o(.ARM.exidx.text.WriteCmd), (8 bytes).
    Removing oled.o(.text.WriteData), (22 bytes).
    Removing oled.o(.ARM.exidx.text.WriteData), (8 bytes).
    Removing oled.o(.text.OLED_WR_Byte), (40 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WR_Byte), (8 bytes).
    Removing oled.o(.text.fill_picture), (112 bytes).
    Removing oled.o(.ARM.exidx.text.fill_picture), (8 bytes).
    Removing oled.o(.text.Delay_50ms), (56 bytes).
    Removing oled.o(.ARM.exidx.text.Delay_50ms), (8 bytes).
    Removing oled.o(.text.Delay_1ms), (54 bytes).
    Removing oled.o(.ARM.exidx.text.Delay_1ms), (8 bytes).
    Removing oled.o(.text.OLED_Set_Pos), (58 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Set_Pos), (8 bytes).
    Removing oled.o(.text.OLED_Display_On), (34 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_On), (8 bytes).
    Removing oled.o(.text.OLED_Display_Off), (34 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_Off), (8 bytes).
    Removing oled.o(.text.OLED_Clear), (106 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.text.OLED_On), (106 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_On), (8 bytes).
    Removing oled.o(.text.OLED_ShowChar), (204 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.text.oled_pow), (48 bytes).
    Removing oled.o(.ARM.exidx.text.oled_pow), (8 bytes).
    Removing oled.o(.text.OLED_ShowNumber), (294 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowNumber), (8 bytes).
    Removing oled.o(.text.OLED_ShowString), (96 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled.o(.text.OLED_ShowCHinese), (184 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowCHinese), (8 bytes).
    Removing oled.o(.text.OLED_DrawBMP), (162 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawBMP), (8 bytes).
    Removing oled.o(.text.OLED_Float), (492 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Float), (8 bytes).
    Removing oled.o(.text.OLED_Float2), (424 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Float2), (8 bytes).
    Removing oled.o(.text.OLED_Num2), (220 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Num2), (8 bytes).
    Removing oled.o(.text.OLED_fuhao_write), (96 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_fuhao_write), (8 bytes).
    Removing oled.o(.text.OLED_Num_write), (100 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Num_write), (8 bytes).
    Removing oled.o(.text.OLED_Num3), (284 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Num3), (8 bytes).
    Removing oled.o(.text.OLED_Num4), (190 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Num4), (8 bytes).
    Removing oled.o(.text.OLED_Num5), (220 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Num5), (8 bytes).
    Removing oled.o(.text.OLED_Init), (238 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.text.DL_Common_updateReg), (40 bytes).
    Removing oled.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing oled.o(.rodata.F6x8), (552 bytes).
    Removing oled.o(.rodata.F8X16), (1520 bytes).
    Removing oled.o(.data.Hzk), (896 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.text.PID_Update), (196 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Update), (8 bytes).
    Removing pwm.o(.text), (0 bytes).
    Removing pwm.o(.text.TB6612_Motor_Stop), (56 bytes).
    Removing pwm.o(.ARM.exidx.text.TB6612_Motor_Stop), (8 bytes).
    Removing pwm.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing pwm.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing pwm.o(.text.AO_Control), (180 bytes).
    Removing pwm.o(.ARM.exidx.text.AO_Control), (8 bytes).
    Removing pwm.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing pwm.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing pwm.o(.text.BO_Control), (180 bytes).
    Removing pwm.o(.ARM.exidx.text.BO_Control), (8 bytes).
    Removing pwm.o(.text.motor_set_duty), (40 bytes).
    Removing pwm.o(.ARM.exidx.text.motor_set_duty), (8 bytes).
    Removing xunji.o(.text), (0 bytes).
    Removing xunji.o(.text.PID_Kongzhi), (112 bytes).
    Removing xunji.o(.ARM.exidx.text.PID_Kongzhi), (8 bytes).
    Removing xunji.o(.text.my_GPIO_readPin), (46 bytes).
    Removing xunji.o(.ARM.exidx.text.my_GPIO_readPin), (8 bytes).
    Removing xunji.o(.text.find_set_bit), (48 bytes).
    Removing xunji.o(.ARM.exidx.text.find_set_bit), (8 bytes).
    Removing xunji.o(.text.DL_GPIO_readPins), (22 bytes).
    Removing xunji.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing xunji.o(.text.xunji), (724 bytes).
    Removing xunji.o(.ARM.exidx.text.xunji), (8 bytes).
    Removing xunji.o(.data.AnglePID_xunji_zuo), (44 bytes).
    Removing xunji.o(.data.AnglePID_xunji_you), (44 bytes).
    Removing xunji.o(.bss.Angle_xunji_zuo), (4 bytes).
    Removing xunji.o(.bss.Angle_xunji_you), (4 bytes).
    Removing xunji.o(.bss.xunji_way), (4 bytes).
    Removing zdt.o(.text), (0 bytes).
    Removing zdt.o(.ARM.exidx.text.My_ABS), (8 bytes).
    Removing zdt.o(.ARM.exidx.text.Stop_Flag_Clear), (8 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_Receive_Data), (8 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_0), (8 bytes).
    Removing zdt.o(.text.Motor_set0), (128 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_set0), (8 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_Enable), (8 bytes).
    Removing zdt.o(.text.Motor_Disable), (64 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_Disable), (8 bytes).
    Removing zdt.o(.text.Motor_Reset), (100 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_Reset), (8 bytes).
    Removing zdt.o(.text.Motor_Clear), (60 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_Clear), (8 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_Init), (8 bytes).
    Removing zdt.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing zdt.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_Enable_All), (8 bytes).
    Removing zdt.o(.text.Motor_Disable_All), (34 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_Disable_All), (8 bytes).
    Removing zdt.o(.text.Motor_Set_PID), (132 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_Set_PID), (8 bytes).
    Removing zdt.o(.text.Motor_Read_Current), (56 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_Read_Current), (8 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_Run), (8 bytes).
    Removing zdt.o(.text.Motor_SetSpeed), (184 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_SetSpeed), (8 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_SetPosition), (8 bytes).
    Removing zdt.o(.text.Motor_SetPosition_A), (276 bytes).
    Removing zdt.o(.ARM.exidx.text.Motor_SetPosition_A), (8 bytes).
    Removing zdt.o(.text.Car_Go), (2788 bytes).
    Removing zdt.o(.ARM.exidx.text.Car_Go), (8 bytes).
    Removing zdt.o(.text.Car_Go_Target), (416 bytes).
    Removing zdt.o(.ARM.exidx.text.Car_Go_Target), (8 bytes).
    Removing zdt.o(.text.Car_Go_Target_A), (452 bytes).
    Removing zdt.o(.ARM.exidx.text.Car_Go_Target_A), (8 bytes).
    Removing zdt.o(.text.Car_Clear), (28 bytes).
    Removing zdt.o(.ARM.exidx.text.Car_Clear), (8 bytes).
    Removing zdt.o(.bss.Rxyanzhen), (5 bytes).
    Removing zdt.o(.bss.wo), (1 bytes).
    Removing zdt.o(.bss.cuf), (4 bytes).
    Removing zdt.o(.data.dir), (4 bytes).
    Removing zdt.o(.bss.Stop_Flag_Motors), (5 bytes).
    Removing zdt.o(.rodata.Motor_Start_Byte), (5 bytes).
    Removing zdt.o(.bss.Car_Go_Target_A.Car_Now_X), (4 bytes).
    Removing zdt.o(.bss.Car_Go_Target_A.Car_Now_Y), (4 bytes).
    Removing zdt.o(.bss.uart2_rxbuff), (1 bytes).
    Removing zdt.o(.bss.RxData), (1 bytes).
    Removing board.o(.text), (0 bytes).
    Removing board.o(.ARM.exidx.text.board_init), (8 bytes).
    Removing board.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing board.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing board.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing board.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing board.o(.text.delay_1us), (16 bytes).
    Removing board.o(.ARM.exidx.text.delay_1us), (8 bytes).
    Removing board.o(.text.delay_1ms), (16 bytes).
    Removing board.o(.ARM.exidx.text.delay_1ms), (8 bytes).
    Removing board.o(.ARM.exidx.text.uart0_send_char), (8 bytes).
    Removing board.o(.ARM.exidx.text.DL_UART_isBusy), (8 bytes).
    Removing board.o(.ARM.exidx.text.DL_UART_transmitData), (8 bytes).
    Removing board.o(.text.uart0_send_string), (60 bytes).
    Removing board.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing board.o(.ARM.exidx.text.UART_sendArray), (8 bytes).
    Removing board.o(.ARM.exidx.text.DL_UART_isTXFIFOFull), (8 bytes).
    Removing board.o(.ARM.exidx.text._sys_exit), (8 bytes).
    Removing board.o(.text.fputc), (44 bytes).
    Removing board.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing board.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing board.o(.ARM.exidx.text.DL_UART_getPendingInterrupt), (8 bytes).
    Removing board.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing board.o(.ARM.exidx.text.UART2_IRQHandler), (8 bytes).
    Removing board.o(.bss.__stdout), (84 bytes).
    Removing hw_lcd.o(.text), (0 bytes).
    Removing hw_lcd.o(.text.spi_write_bus), (44 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.spi_write_bus), (8 bytes).
    Removing hw_lcd.o(.text.DL_SPI_transmitData8), (22 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_SPI_transmitData8), (8 bytes).
    Removing hw_lcd.o(.text.DL_SPI_isBusy), (24 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_SPI_isBusy), (8 bytes).
    Removing hw_lcd.o(.text.LCD_Writ_Bus), (48 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Writ_Bus), (8 bytes).
    Removing hw_lcd.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_lcd.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing hw_lcd.o(.text.LCD_WR_DATA8), (20 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA8), (8 bytes).
    Removing hw_lcd.o(.text.LCD_WR_DATA), (28 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_DATA), (8 bytes).
    Removing hw_lcd.o(.text.LCD_WR_REG), (48 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_WR_REG), (8 bytes).
    Removing hw_lcd.o(.text.LCD_Address_Set), (62 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Address_Set), (8 bytes).
    Removing hw_lcd.o(.text.lcd_init), (452 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.lcd_init), (8 bytes).
    Removing hw_lcd.o(.text.LCD_Fill), (92 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_Fill), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawPoint), (32 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawPoint), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawLine), (268 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawLine), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawVerrticalLine), (66 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawVerrticalLine), (8 bytes).
    Removing hw_lcd.o(.text.LCD_DrawRectangle), (82 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_DrawRectangle), (8 bytes).
    Removing hw_lcd.o(.text.Draw_Circle), (220 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.Draw_Circle), (8 bytes).
    Removing hw_lcd.o(.text.Drawarc), (338 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.Drawarc), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ArcRect), (330 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ArcRect), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese), (296 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese12x12), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese12x12), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese16x16), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese16x16), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese24x24), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese24x24), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChinese32x32), (412 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChinese32x32), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowChar), (476 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowChar), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowString), (98 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowString), (8 bytes).
    Removing hw_lcd.o(.text.mypow), (48 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.mypow), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowIntNum), (244 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowIntNum), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowFloatNum1), (252 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowFloatNum1), (8 bytes).
    Removing hw_lcd.o(.text.LCD_ShowPicture), (128 bytes).
    Removing hw_lcd.o(.ARM.exidx.text.LCD_ShowPicture), (8 bytes).
    Removing hw_lcd.o(.rodata.ascii_1206), (1140 bytes).
    Removing hw_lcd.o(.rodata.ascii_1608), (1520 bytes).
    Removing hw_lcd.o(.rodata.ascii_2412), (4560 bytes).
    Removing hw_lcd.o(.rodata.ascii_3216), (6080 bytes).
    Removing hw_lcd.o(.rodata.tfont12), (0 bytes).
    Removing hw_lcd.o(.rodata.tfont16), (0 bytes).
    Removing hw_lcd.o(.rodata.tfont24), (0 bytes).
    Removing hw_lcd.o(.rodata.tfont32), (0 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.text.Key_Read), (160 bytes).
    Removing key.o(.ARM.exidx.text.Key_Read), (8 bytes).
    Removing key.o(.text.Key_ReadAll), (60 bytes).
    Removing key.o(.ARM.exidx.text.Key_ReadAll), (8 bytes).
    Removing key.o(.data.keys), (80 bytes).
    Removing bsp_mpu6050.o(.text), (0 bytes).
    Removing bsp_mpu6050.o(.text.IIC_Start), (96 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.IIC_Start), (8 bytes).
    Removing bsp_mpu6050.o(.text.DL_GPIO_initDigitalOutput), (24 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing bsp_mpu6050.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing bsp_mpu6050.o(.text.DL_GPIO_enableOutput), (24 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing bsp_mpu6050.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing bsp_mpu6050.o(.text.IIC_Stop), (88 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.IIC_Stop), (8 bytes).
    Removing bsp_mpu6050.o(.text.IIC_Send_Ack), (132 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.IIC_Send_Ack), (8 bytes).
    Removing bsp_mpu6050.o(.text.I2C_WaitAck), (192 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.I2C_WaitAck), (8 bytes).
    Removing bsp_mpu6050.o(.text.DL_GPIO_initDigitalInput), (28 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing bsp_mpu6050.o(.text.DL_GPIO_readPins), (22 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing bsp_mpu6050.o(.text.Send_Byte), (160 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.Send_Byte), (8 bytes).
    Removing bsp_mpu6050.o(.text.Read_Byte), (160 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.Read_Byte), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050_WriteReg), (180 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050_ReadData), (214 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_ReadData), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_Gyro_Fsr), (28 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Gyro_Fsr), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_Accel_Fsr), (28 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Accel_Fsr), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_LPF), (142 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_LPF), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU_Set_Rate), (104 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU_Set_Rate), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050ReadGyro), (84 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadGyro), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050ReadAcc), (84 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadAcc), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050_GetTemp), (88 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_GetTemp), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050ReadID), (84 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050ReadID), (8 bytes).
    Removing bsp_mpu6050.o(.text.MPU6050_Init), (212 bytes).
    Removing bsp_mpu6050.o(.ARM.exidx.text.MPU6050_Init), (8 bytes).
    Removing bsp_mpu6050.o(.rodata.str1.1), (27 bytes).
    Removing inv_mpu.o(.text), (0 bytes).
    Removing inv_mpu.o(.text.mpu_reg_dump), (140 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_reg_dump), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_reg), (108 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_reg), (8 bytes).
    Removing inv_mpu.o(.text.mpu_init), (532 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_init), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_gyro_fsr), (196 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_gyro_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_accel_fsr), (228 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_accel_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_lpf), (208 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_lpf), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_sample_rate), (236 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_configure_fifo), (188 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_configure_fifo), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_bypass), (416 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_bypass), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_sensors), (304 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_sensors), (8 bytes).
    Removing inv_mpu.o(.text.mpu_lp_accel_mode), (272 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_lp_accel_mode), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_int_latched), (156 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_int_latched), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_gyro_reg), (132 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_reg), (8 bytes).
    Removing inv_mpu.o(.text.mget_ms), (8 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mget_ms), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_accel_reg), (132 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_reg), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_temperature), (188 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_temperature), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_accel_bias), (356 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_accel_bias), (8 bytes).
    Removing inv_mpu.o(.text.mpu_reset_fifo), (556 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_reset_fifo), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_gyro_fsr), (96 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_accel_fsr), (116 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_lpf), (124 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_lpf), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_sample_rate), (52 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_sample_rate), (16 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_compass_sample_rate), (14 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_gyro_sens), (112 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_gyro_sens), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_accel_sens), (128 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_accel_sens), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_fifo_config), (24 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_fifo_config), (8 bytes).
    Removing inv_mpu.o(.text.set_int_enable), (212 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.set_int_enable), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_power_state), (40 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_power_state), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_int_status), (88 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_int_status), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_fifo), (684 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_fifo), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_fifo_stream), (264 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_fifo_stream), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_int_level), (24 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_int_level), (8 bytes).
    Removing inv_mpu.o(.text.mpu_run_self_test), (372 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_run_self_test), (8 bytes).
    Removing inv_mpu.o(.text.mpu_set_dmp_state), (184 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_set_dmp_state), (8 bytes).
    Removing inv_mpu.o(.text.get_st_biases), (1188 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.get_st_biases), (8 bytes).
    Removing inv_mpu.o(.text.accel_self_test), (220 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.accel_self_test), (8 bytes).
    Removing inv_mpu.o(.text.gyro_self_test), (304 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.gyro_self_test), (8 bytes).
    Removing inv_mpu.o(.text.mpu_write_mem), (172 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_write_mem), (8 bytes).
    Removing inv_mpu.o(.text.mpu_read_mem), (172 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_read_mem), (8 bytes).
    Removing inv_mpu.o(.text.mpu_load_firmware), (292 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_load_firmware), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_dmp_state), (24 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_dmp_state), (8 bytes).
    Removing inv_mpu.o(.text.setup_compass), (6 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.setup_compass), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_reg), (14 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_reg), (8 bytes).
    Removing inv_mpu.o(.text.mpu_get_compass_fsr), (12 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_get_compass_fsr), (8 bytes).
    Removing inv_mpu.o(.text.mpu_lp_motion_interrupt), (656 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_lp_motion_interrupt), (8 bytes).
    Removing inv_mpu.o(.text.run_self_test), (152 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.run_self_test), (8 bytes).
    Removing inv_mpu.o(.text.inv_orientation_matrix_to_scalar), (62 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.inv_orientation_matrix_to_scalar), (8 bytes).
    Removing inv_mpu.o(.text.inv_row_2_scale), (150 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.inv_row_2_scale), (8 bytes).
    Removing inv_mpu.o(.text.mpu_dmp_init), (280 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_dmp_init), (8 bytes).
    Removing inv_mpu.o(.text.mpu_dmp_get_data), (532 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.mpu_dmp_get_data), (8 bytes).
    Removing inv_mpu.o(.text.get_accel_prod_shift), (236 bytes).
    Removing inv_mpu.o(.ARM.exidx.text.get_accel_prod_shift), (8 bytes).
    Removing inv_mpu.o(.rodata.reg), (27 bytes).
    Removing inv_mpu.o(.rodata.hw), (12 bytes).
    Removing inv_mpu.o(.rodata.test), (40 bytes).
    Removing inv_mpu.o(.data.st), (44 bytes).
    Removing inv_mpu.o(.rodata.str1.1), (158 bytes).
    Removing inv_mpu.o(.data.gyro_orientation), (9 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text), (0 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_load_motion_driver_firmware), (28 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_load_motion_driver_firmware), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_orientation), (376 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_orientation), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_gyro_bias), (312 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_gyro_bias), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_accel_bias), (264 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_accel_bias), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_fifo_rate), (152 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_fifo_rate), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_fifo_rate), (24 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_fifo_rate), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_thresh), (568 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_thresh), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_axes), (102 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_axes), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_count), (68 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_tap_time_multi), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_tap_time_multi), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_thresh), (68 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_thresh), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_time), (50 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_shake_reject_timeout), (50 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_shake_reject_timeout), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_step_count), (86 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_step_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_step_count), (42 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_step_count), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_pedometer_walk_time), (90 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_pedometer_walk_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_pedometer_walk_time), (52 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_pedometer_walk_time), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_feature), (632 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_feature), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_gyro_cal), (96 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_gyro_cal), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_lp_quat), (72 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_lp_quat), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_enable_6x_lp_quat), (72 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_enable_6x_lp_quat), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_get_enabled_features), (24 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_get_enabled_features), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_set_interrupt_mode), (128 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_set_interrupt_mode), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_read_fifo), (500 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_read_fifo), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.decode_gesture), (140 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.decode_gesture), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_register_tap_cb), (20 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_tap_cb), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text.dmp_register_android_orient_cb), (20 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.ARM.exidx.text.dmp_register_android_orient_cb), (8 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata.dmp_memory), (3062 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.gyro_axes), (3 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.accel_axes), (3 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.gyro_sign), (3 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_orientation.accel_sign), (3 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.bss.dmp), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_fifo_rate.regs_end), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_enable_gyro_cal.regs), (9 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_enable_gyro_cal.regs.1), (9 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_interrupt_mode.regs_continuous), (11 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rodata..L__const.dmp_set_interrupt_mode.regs_gesture), (11 bytes).
    Removing wit.o(.text), (0 bytes).
    Removing wit.o(.text.WIT_Init), (68 bytes).
    Removing wit.o(.ARM.exidx.text.WIT_Init), (8 bytes).
    Removing wit.o(.text.DL_DMA_setSrcAddr), (40 bytes).
    Removing wit.o(.ARM.exidx.text.DL_DMA_setSrcAddr), (8 bytes).
    Removing wit.o(.ARM.exidx.text.DL_DMA_setDestAddr), (8 bytes).
    Removing wit.o(.ARM.exidx.text.DL_DMA_setTransferSize), (8 bytes).
    Removing wit.o(.ARM.exidx.text.DL_DMA_enableChannel), (8 bytes).
    Removing wit.o(.text.__NVIC_EnableIRQ), (44 bytes).
    Removing wit.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing wit.o(.ARM.exidx.text.UART3_IRQHandler), (8 bytes).
    Removing wit.o(.ARM.exidx.text.DL_DMA_disableChannel), (8 bytes).
    Removing wit.o(.ARM.exidx.text.DL_DMA_getTransferSize), (8 bytes).
    Removing wit.o(.ARM.exidx.text.DL_UART_isRXFIFOEmpty), (8 bytes).
    Removing wit.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (44 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (48 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (16 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (44 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (80 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (112 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).

823 unused section(s) (total 67118 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.c                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate_common.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  llsdiv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  lludiv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  rtudiv10.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _c16rtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llmul.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv6m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec_accurate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  rand.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  rand.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  dcmp.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/cfplib/cmpret.c                 0x00000000   Number         0  cmpret.o ABSOLUTE
    ../fplib/cfplib/d2f.c                    0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/cfplib/daddsub.c                0x00000000   Number         0  daddsub.o ABSOLUTE
    ../fplib/cfplib/dcmpin.c                 0x00000000   Number         0  dcmpin.o ABSOLUTE
    ../fplib/cfplib/ddiv.c                   0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/cfplib/dmul.c                   0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/cfplib/dsqrt.c                  0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../fplib/cfplib/f2d.c                    0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/cfplib/fcmpin.c                 0x00000000   Number         0  fcmpin.o ABSOLUTE
    ../fplib/cfplib/fdiv.c                   0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/cfplib/ieee_status.c            0x00000000   Number         0  ieee_status.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  dnan2.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  fnan2.o ABSOLUTE
    ../fplib/cfplib/retnan.c                 0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/cfplib/scalbn.c                 0x00000000   Number         0  dscalbn.o ABSOLUTE
    ../fplib/deqf6m.s                        0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dgeqf6m.s                       0x00000000   Number         0  dgef.o ABSOLUTE
    ../fplib/dleqf6m.s                       0x00000000   Number         0  dlef.o ABSOLUTE
    ../fplib/faddsub6m.s                     0x00000000   Number         0  faddsub.o ABSOLUTE
    ../fplib/feqf6m.s                        0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/fgeqf6m.s                       0x00000000   Number         0  fgef.o ABSOLUTE
    ../fplib/fleqf6m.s                       0x00000000   Number         0  flef.o ABSOLUTE
    ../fplib/fmul6m.s                        0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    Key.c                                    0x00000000   Number         0  key.o ABSOLUTE
    PID.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    PWM.c                                    0x00000000   Number         0  pwm.o ABSOLUTE
    ZDT.c                                    0x00000000   Number         0  zdt.o ABSOLUTE
    app_ui.c                                 0x00000000   Number         0  app_ui.o ABSOLUTE
    board.c                                  0x00000000   Number         0  board.o ABSOLUTE
    bsp_mpu6050.c                            0x00000000   Number         0  bsp_mpu6050.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    encoder.c                                0x00000000   Number         0  encoder.o ABSOLUTE
    hw_lcd.c                                 0x00000000   Number         0  hw_lcd.o ABSOLUTE
    inv_mpu.c                                0x00000000   Number         0  inv_mpu.o ABSOLUTE
    inv_mpu_dmp_motion_driver.c              0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    oled.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    wit.c                                    0x00000000   Number         0  wit.o ABSOLUTE
    xunji.c                                  0x00000000   Number         0  xunji.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_null                           0x00000120   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000128   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x00000144   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000146   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000146   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000148   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0000014a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0000014a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0000014c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0000014c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0000014c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x00000152   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x00000152   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000156   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000156   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0000015e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x00000160   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x00000160   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x00000164   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0000016c   Section       48  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x0000019c   Section        0  heapauxi.o(.text)
    .text                                    0x000001a4   Section        0  d2f.o(.text)
    .text                                    0x00000220   Section        0  ddiv.o(.text)
    .text                                    0x00000668   Section        0  dfixi.o(.text)
    .text                                    0x000006d4   Section        0  dflti.o(.text)
    .text                                    0x0000072c   Section        0  dmul.o(.text)
    .text                                    0x00000974   Section        8  libspace.o(.text)
    .text                                    0x0000097c   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x000009ba   Section        0  exit.o(.text)
    [Anonymous Symbol]                       0x000009ca   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    DL_Common_updateReg                      0x000009d5   Thumb Code    40  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x000009d4   Section        0  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    DL_DMA_disableChannel                    0x000009fd   Thumb Code    38  wit.o(.text.DL_DMA_disableChannel)
    [Anonymous Symbol]                       0x000009fc   Section        0  wit.o(.text.DL_DMA_disableChannel)
    DL_DMA_enableChannel                     0x00000a23   Thumb Code    38  wit.o(.text.DL_DMA_enableChannel)
    [Anonymous Symbol]                       0x00000a22   Section        0  wit.o(.text.DL_DMA_enableChannel)
    DL_DMA_getTransferSize                   0x00000a49   Thumb Code    32  wit.o(.text.DL_DMA_getTransferSize)
    [Anonymous Symbol]                       0x00000a48   Section        0  wit.o(.text.DL_DMA_getTransferSize)
    [Anonymous Symbol]                       0x00000a68   Section        0  dl_dma.o(.text.DL_DMA_initChannel)
    __arm_cp.0_0                             0x00000aa8   Number         4  dl_dma.o(.text.DL_DMA_initChannel)
    DL_DMA_setDestAddr                       0x00000aad   Thumb Code    36  wit.o(.text.DL_DMA_setDestAddr)
    [Anonymous Symbol]                       0x00000aac   Section        0  wit.o(.text.DL_DMA_setDestAddr)
    __arm_cp.2_0                             0x00000ad0   Number         4  wit.o(.text.DL_DMA_setDestAddr)
    DL_DMA_setTransferSize                   0x00000ad5   Thumb Code    44  wit.o(.text.DL_DMA_setTransferSize)
    [Anonymous Symbol]                       0x00000ad4   Section        0  wit.o(.text.DL_DMA_setTransferSize)
    __arm_cp.3_0                             0x00000b00   Number         4  wit.o(.text.DL_DMA_setTransferSize)
    DL_GPIO_clearInterruptStatus             0x00000b05   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00000b04   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearInterruptStatus             0x00000b1d   Thumb Code    24  encoder.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00000b1c   Section        0  encoder.o(.text.DL_GPIO_clearInterruptStatus)
    __arm_cp.6_0                             0x00000b34   Number         4  encoder.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearPins                        0x00000b39   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000b38   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableHiZ                        0x00000b4d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ)
    [Anonymous Symbol]                       0x00000b4c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ)
    DL_GPIO_enableInterrupt                  0x00000b65   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    [Anonymous Symbol]                       0x00000b64   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    __arm_cp.38_0                            0x00000b7c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    DL_GPIO_enableOutput                     0x00000b81   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x00000b80   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    __arm_cp.27_0                            0x00000b94   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x00000b99   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x00000b98   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_getEnabledInterruptStatus        0x00000bad   Thumb Code    20  encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    [Anonymous Symbol]                       0x00000bac   Section        0  encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    __arm_cp.4_0                             0x00000bc0   Number         4  encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    DL_GPIO_initDigitalInputFeatures         0x00000bc5   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    [Anonymous Symbol]                       0x00000bc4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    __arm_cp.32_0                            0x00000bf0   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    DL_GPIO_initDigitalOutput                0x00000bf5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x00000bf4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initPeripheralAnalogFunction     0x00000c09   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    [Anonymous Symbol]                       0x00000c08   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    DL_GPIO_initPeripheralInputFunction      0x00000c1d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    [Anonymous Symbol]                       0x00000c1c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    DL_GPIO_initPeripheralInputFunctionFeatures 0x00000c35   Thumb Code    52  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    [Anonymous Symbol]                       0x00000c34   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    __arm_cp.28_0                            0x00000c68   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    DL_GPIO_initPeripheralOutputFunction     0x00000c6d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    [Anonymous Symbol]                       0x00000c6c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    __arm_cp.26_0                            0x00000c84   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    DL_GPIO_readPins                         0x00000c89   Thumb Code    22  encoder.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00000c88   Section        0  encoder.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x00000ca1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x00000ca0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setLowerPinsPolarity             0x00000cb1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    [Anonymous Symbol]                       0x00000cb0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    __arm_cp.35_0                            0x00000cc8   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    DL_GPIO_setPins                          0x00000ccd   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00000ccc   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    __arm_cp.34_0                            0x00000ce0   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    DL_GPIO_setUpperPinsPolarity             0x00000ce5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    [Anonymous Symbol]                       0x00000ce4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    DL_I2C_enableAnalogGlitchFilter          0x00000cfd   Thumb Code    24  ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter)
    [Anonymous Symbol]                       0x00000cfc   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter)
    DL_I2C_enableController                  0x00000d15   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enableController)
    [Anonymous Symbol]                       0x00000d14   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableController)
    DL_I2C_enableControllerClockStretching   0x00000d29   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    [Anonymous Symbol]                       0x00000d28   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    __arm_cp.55_0                            0x00000d3c   Number         4  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    DL_I2C_enablePower                       0x00000d41   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    [Anonymous Symbol]                       0x00000d40   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    DL_I2C_reset                             0x00000d55   Thumb Code    16  ti_msp_dl_config.o(.text.DL_I2C_reset)
    [Anonymous Symbol]                       0x00000d54   Section        0  ti_msp_dl_config.o(.text.DL_I2C_reset)
    DL_I2C_resetControllerTransfer           0x00000d65   Thumb Code    16  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    [Anonymous Symbol]                       0x00000d64   Section        0  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    __arm_cp.51_0                            0x00000d74   Number         4  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    DL_I2C_setAnalogGlitchFilterPulseWidth   0x00000d79   Thumb Code    38  ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
    [Anonymous Symbol]                       0x00000d78   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
    [Anonymous Symbol]                       0x00000d9e   Section        0  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_I2C_setControllerRXFIFOThreshold      0x00000dc5   Thumb Code    36  ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold)
    [Anonymous Symbol]                       0x00000dc4   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold)
    DL_I2C_setControllerTXFIFOThreshold      0x00000de9   Thumb Code    36  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    [Anonymous Symbol]                       0x00000de8   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    __arm_cp.53_0                            0x00000e0c   Number         4  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    DL_I2C_setTimerPeriod                    0x00000e11   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    [Anonymous Symbol]                       0x00000e10   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    __arm_cp.52_0                            0x00000e24   Number         4  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    DL_SPI_enable                            0x00000e29   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SPI_enable)
    [Anonymous Symbol]                       0x00000e28   Section        0  ti_msp_dl_config.o(.text.DL_SPI_enable)
    __arm_cp.67_0                            0x00000e3c   Number         4  ti_msp_dl_config.o(.text.DL_SPI_enable)
    DL_SPI_enablePower                       0x00000e41   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    [Anonymous Symbol]                       0x00000e40   Section        0  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    __arm_cp.24_0                            0x00000e54   Number         4  ti_msp_dl_config.o(.text.DL_SPI_enablePower)
    [Anonymous Symbol]                       0x00000e58   Section        0  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_0                             0x00000e94   Number         4  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_1                             0x00000e98   Number         4  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_reset                             0x00000e9d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SPI_reset)
    [Anonymous Symbol]                       0x00000e9c   Section        0  ti_msp_dl_config.o(.text.DL_SPI_reset)
    __arm_cp.19_0                            0x00000eac   Number         4  ti_msp_dl_config.o(.text.DL_SPI_reset)
    __arm_cp.19_1                            0x00000eb0   Number         4  ti_msp_dl_config.o(.text.DL_SPI_reset)
    DL_SPI_setBitRateSerialClockDivider      0x00000eb5   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    [Anonymous Symbol]                       0x00000eb4   Section        0  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    __arm_cp.65_1                            0x00000ed0   Number         4  ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider)
    [Anonymous Symbol]                       0x00000ed4   Section        0  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_SPI_setFIFOThreshold                  0x00000ee9   Thumb Code    44  ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold)
    [Anonymous Symbol]                       0x00000ee8   Section        0  ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold)
    DL_SYSCTL_disableHFXT                    0x00000f15   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    [Anonymous Symbol]                       0x00000f14   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    DL_SYSCTL_disableSYSPLL                  0x00000f21   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    [Anonymous Symbol]                       0x00000f20   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    __arm_cp.42_0                            0x00000f30   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    DL_SYSCTL_enableMFCLK                    0x00000f35   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    [Anonymous Symbol]                       0x00000f34   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    __arm_cp.43_0                            0x00000f44   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    DL_SYSCTL_setBORThreshold                0x00000f49   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    [Anonymous Symbol]                       0x00000f48   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    __arm_cp.39_0                            0x00000f5c   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    DL_SYSCTL_setSYSOSCFreq                  0x00000f61   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    [Anonymous Symbol]                       0x00000f60   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    __arm_cp.40_0                            0x00000f78   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    DL_SYSTICK_enable                        0x00000f7d   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    [Anonymous Symbol]                       0x00000f7c   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    DL_SYSTICK_init                          0x00000f89   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00000f88   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.69_0                            0x00000fa4   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.69_1                            0x00000fa8   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.69_2                            0x00000fac   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    DL_Timer_enableClock                     0x00000fb1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    [Anonymous Symbol]                       0x00000fb0   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    __arm_cp.46_0                            0x00000fc0   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    DL_Timer_enableInterrupt                 0x00000fc5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    [Anonymous Symbol]                       0x00000fc4   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    DL_Timer_enablePower                     0x00000fdd   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00000fdc   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    __arm_cp.21_0                            0x00000ff0   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00000ff4   Section        0  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_1                            0x000010e8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_2                            0x000010ec   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_4                            0x000010f0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_5                            0x000010f4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_6                            0x000010f8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    [Anonymous Symbol]                       0x000010fc   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x000011dc   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x000011e0   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_3                             0x000011e4   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_reset                           0x000011e9   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_reset)
    [Anonymous Symbol]                       0x000011e8   Section        0  ti_msp_dl_config.o(.text.DL_Timer_reset)
    __arm_cp.16_0                            0x000011f8   Number         4  ti_msp_dl_config.o(.text.DL_Timer_reset)
    __arm_cp.16_1                            0x000011fc   Number         4  ti_msp_dl_config.o(.text.DL_Timer_reset)
    DL_Timer_setCCPDirection                 0x00001201   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00001200   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00001214   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.23_0                            0x0000122c   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00001230   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.27_0                            0x00001244   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00001248   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00001254   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00001258   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00001270   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_Timer_setCounterControl               0x00001275   Thumb Code    52  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    [Anonymous Symbol]                       0x00001274   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.45_0                            0x000012a8   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.45_1                            0x000012ac   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    [Anonymous Symbol]                       0x000012b0   Section        0  dl_uart.o(.text.DL_UART_drainRXFIFO)
    __arm_cp.10_0                            0x000012d4   Number         4  dl_uart.o(.text.DL_UART_drainRXFIFO)
    DL_UART_enable                           0x000012d9   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enable)
    [Anonymous Symbol]                       0x000012d8   Section        0  ti_msp_dl_config.o(.text.DL_UART_enable)
    DL_UART_enableDMAReceiveEvent            0x000012f1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    [Anonymous Symbol]                       0x000012f0   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    __arm_cp.61_0                            0x00001304   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    DL_UART_enableFIFOs                      0x00001309   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableFIFOs)
    [Anonymous Symbol]                       0x00001308   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableFIFOs)
    DL_UART_enableInterrupt                  0x00001321   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    [Anonymous Symbol]                       0x00001320   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    __arm_cp.59_0                            0x00001338   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    DL_UART_enablePower                      0x0000133d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    [Anonymous Symbol]                       0x0000133c   Section        0  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    __arm_cp.23_0                            0x00001350   Number         4  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    DL_UART_getPendingInterrupt              0x00001355   Thumb Code    18  board.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001354   Section        0  board.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001368   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x000013a8   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x000013ac   Number         4  dl_uart.o(.text.DL_UART_init)
    DL_UART_isBusy                           0x000013b1   Thumb Code    20  board.o(.text.DL_UART_isBusy)
    [Anonymous Symbol]                       0x000013b0   Section        0  board.o(.text.DL_UART_isBusy)
    DL_UART_isRXFIFOEmpty                    0x000013c5   Thumb Code    20  wit.o(.text.DL_UART_isRXFIFOEmpty)
    [Anonymous Symbol]                       0x000013c4   Section        0  wit.o(.text.DL_UART_isRXFIFOEmpty)
    DL_UART_isTXFIFOFull                     0x000013d9   Thumb Code    20  board.o(.text.DL_UART_isTXFIFOFull)
    [Anonymous Symbol]                       0x000013d8   Section        0  board.o(.text.DL_UART_isTXFIFOFull)
    __arm_cp.12_0                            0x000013ec   Number         4  board.o(.text.DL_UART_isTXFIFOFull)
    DL_UART_receiveData                      0x000013f1   Thumb Code    16  board.o(.text.DL_UART_receiveData)
    [Anonymous Symbol]                       0x000013f0   Section        0  board.o(.text.DL_UART_receiveData)
    DL_UART_receiveData                      0x00001401   Thumb Code    16  wit.o(.text.DL_UART_receiveData)
    [Anonymous Symbol]                       0x00001400   Section        0  wit.o(.text.DL_UART_receiveData)
    __arm_cp.10_0                            0x00001410   Number         4  wit.o(.text.DL_UART_receiveData)
    DL_UART_reset                            0x00001415   Thumb Code    16  ti_msp_dl_config.o(.text.DL_UART_reset)
    [Anonymous Symbol]                       0x00001414   Section        0  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.18_0                            0x00001424   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.18_1                            0x00001428   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    DL_UART_setBaudRateDivisor               0x0000142d   Thumb Code    60  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x0000142c   Section        0  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.58_0                            0x00001468   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.58_1                            0x0000146c   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.58_2                            0x00001470   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.58_3                            0x00001474   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x00001478   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_setOversampling                  0x0000148b   Thumb Code    30  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    [Anonymous Symbol]                       0x0000148a   Section        0  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    DL_UART_setRXFIFOThreshold               0x000014a9   Thumb Code    36  ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold)
    [Anonymous Symbol]                       0x000014a8   Section        0  ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold)
    DL_UART_setRXInterruptTimeout            0x000014cd   Thumb Code    32  ti_msp_dl_config.o(.text.DL_UART_setRXInterruptTimeout)
    [Anonymous Symbol]                       0x000014cc   Section        0  ti_msp_dl_config.o(.text.DL_UART_setRXInterruptTimeout)
    __arm_cp.64_0                            0x000014ec   Number         4  ti_msp_dl_config.o(.text.DL_UART_setRXInterruptTimeout)
    DL_UART_transmitData                     0x000014f1   Thumb Code    22  board.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x000014f0   Section        0  board.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x00001508   Section        0  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.3_0                             0x00001614   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.3_1                             0x00001618   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.3_2                             0x0000161c   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x00001620   Section        0  zdt.o(.text.Motor_0)
    [Anonymous Symbol]                       0x00001658   Section        0  zdt.o(.text.Motor_Enable)
    [Anonymous Symbol]                       0x00001694   Section        0  zdt.o(.text.Motor_Enable_All)
    [Anonymous Symbol]                       0x000016b6   Section        0  zdt.o(.text.Motor_Init)
    [Anonymous Symbol]                       0x000016e8   Section        0  zdt.o(.text.Motor_Receive_Data)
    __arm_cp.2_0                             0x00001850   Number         4  zdt.o(.text.Motor_Receive_Data)
    __arm_cp.2_1                             0x00001854   Number         4  zdt.o(.text.Motor_Receive_Data)
    __arm_cp.2_2                             0x00001858   Number         4  zdt.o(.text.Motor_Receive_Data)
    __arm_cp.2_4                             0x0000185c   Number         4  zdt.o(.text.Motor_Receive_Data)
    __arm_cp.2_5                             0x00001860   Number         4  zdt.o(.text.Motor_Receive_Data)
    [Anonymous Symbol]                       0x00001864   Section        0  zdt.o(.text.Motor_Run)
    [Anonymous Symbol]                       0x0000189c   Section        0  zdt.o(.text.Motor_SetPosition)
    __arm_cp.18_0                            0x000019b0   Number         4  zdt.o(.text.Motor_SetPosition)
    __arm_cp.18_1                            0x000019b4   Number         4  zdt.o(.text.Motor_SetPosition)
    [Anonymous Symbol]                       0x000019b8   Section        0  zdt.o(.text.My_ABS)
    [Anonymous Symbol]                       0x000019d8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init)
    __arm_cp.68_0                            0x000019e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init)
    __arm_cp.68_1                            0x000019ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init)
    [Anonymous Symbol]                       0x000019f0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    [Anonymous Symbol]                       0x000019f8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00001c4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00001c50   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00001c54   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00001c58   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00001c5c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00001c60   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.6_0                             0x00001cb0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    [Anonymous Symbol]                       0x00001cb4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_0                             0x00001d38   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_2                             0x00001d3c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    [Anonymous Symbol]                       0x00001d40   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    __arm_cp.10_0                            0x00001d74   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    __arm_cp.10_2                            0x00001d78   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    [Anonymous Symbol]                       0x00001d7c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00001da6   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00001db4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.5_0                             0x00001de4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.5_2                             0x00001de8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    [Anonymous Symbol]                       0x00001dec   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_0                             0x00001e30   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_2                             0x00001e34   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00001e38   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.9_0                             0x00001e74   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.9_2                             0x00001e78   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    [Anonymous Symbol]                       0x00001e7c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init)
    __arm_cp.8_0                             0x00001ed8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init)
    __arm_cp.8_2                             0x00001edc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init)
    [Anonymous Symbol]                       0x00001ee0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00001f28   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x00001f2c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_2                             0x00001f30   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00001f34   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00001fc0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00001fc4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00001fc8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00001fcc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00001fd0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00001fd4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_8                             0x00001fd8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00001fdc   Section        0  zdt.o(.text.Stop_Flag_Clear)
    __arm_cp.1_0                             0x00001fec   Number         4  zdt.o(.text.Stop_Flag_Clear)
    __arm_cp.1_1                             0x00001ff0   Number         4  zdt.o(.text.Stop_Flag_Clear)
    __arm_cp.1_2                             0x00001ff4   Number         4  zdt.o(.text.Stop_Flag_Clear)
    [Anonymous Symbol]                       0x00001ff8   Section        0  board.o(.text.UART0_IRQHandler)
    __arm_cp.15_0                            0x00002050   Number         4  board.o(.text.UART0_IRQHandler)
    __arm_cp.15_1                            0x00002054   Number         4  board.o(.text.UART0_IRQHandler)
    __arm_cp.15_2                            0x00002058   Number         4  board.o(.text.UART0_IRQHandler)
    __arm_cp.15_3                            0x0000205c   Number         4  board.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x00002060   Section        0  board.o(.text.UART2_IRQHandler)
    __arm_cp.18_0                            0x0000208c   Number         4  board.o(.text.UART2_IRQHandler)
    [Anonymous Symbol]                       0x00002090   Section        0  wit.o(.text.UART3_IRQHandler)
    __arm_cp.6_0                             0x000023d4   Number         4  wit.o(.text.UART3_IRQHandler)
    __arm_cp.6_1                             0x000023d8   Number         4  wit.o(.text.UART3_IRQHandler)
    __arm_cp.6_2                             0x000023dc   Number         4  wit.o(.text.UART3_IRQHandler)
    __arm_cp.6_3                             0x000023e0   Number         4  wit.o(.text.UART3_IRQHandler)
    __arm_cp.6_4                             0x000023e4   Number         4  wit.o(.text.UART3_IRQHandler)
    __arm_cp.6_5                             0x000023e8   Number         4  wit.o(.text.UART3_IRQHandler)
    __arm_cp.6_6                             0x000023ec   Number         4  wit.o(.text.UART3_IRQHandler)
    __arm_cp.6_7                             0x000023f0   Number         4  wit.o(.text.UART3_IRQHandler)
    __arm_cp.6_8                             0x000023f4   Number         4  wit.o(.text.UART3_IRQHandler)
    [Anonymous Symbol]                       0x000023f8   Section        0  board.o(.text.UART_sendArray)
    __NVIC_ClearPendingIRQ                   0x0000244d   Thumb Code    40  zdt.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x0000244c   Section        0  zdt.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_ClearPendingIRQ                   0x00002475   Thumb Code    40  board.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00002474   Section        0  board.o(.text.__NVIC_ClearPendingIRQ)
    __arm_cp.1_0                             0x0000249c   Number         4  board.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_EnableIRQ                         0x000024a1   Thumb Code    40  zdt.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x000024a0   Section        0  zdt.o(.text.__NVIC_EnableIRQ)
    __NVIC_EnableIRQ                         0x000024c9   Thumb Code    40  board.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x000024c8   Section        0  board.o(.text.__NVIC_EnableIRQ)
    __arm_cp.2_0                             0x000024f0   Number         4  board.o(.text.__NVIC_EnableIRQ)
    __NVIC_SetPriority                       0x000024f5   Thumb Code   124  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x000024f4   Section        0  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.44_0                            0x00002570   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.44_1                            0x00002574   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00002578   Section        0  board.o(.text._sys_exit)
    [Anonymous Symbol]                       0x00002580   Section        0  board.o(.text.board_init)
    [Anonymous Symbol]                       0x0000259a   Section        0  board.o(.text.delay_ms)
    [Anonymous Symbol]                       0x000025b0   Section        0  board.o(.text.delay_us)
    __arm_cp.3_0                             0x0000261c   Number         4  board.o(.text.delay_us)
    __arm_cp.3_1                             0x00002620   Number         4  board.o(.text.delay_us)
    [Anonymous Symbol]                       0x00002624   Section        0  empty.o(.text.main)
    __arm_cp.0_0                             0x000026a0   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x000026a4   Section        0  board.o(.text.uart0_send_char)
    __arm_cp.7_0                             0x000026cc   Number         4  board.o(.text.uart0_send_char)
    ddiv_reciptbl                            0x000026d0   Data         128  ddiv.o(.constdata)
    .constdata                               0x000026d0   Section      128  ddiv.o(.constdata)
    x$fpl$usenofp                            0x000026d0   Section        0  usenofp.o(x$fpl$usenofp)
    gDMA_WITConfig                           0x00002750   Data          24  ti_msp_dl_config.o(.rodata.gDMA_WITConfig)
    [Anonymous Symbol]                       0x00002750   Section        0  ti_msp_dl_config.o(.rodata.gDMA_WITConfig)
    gI2C_0ClockConfig                        0x00002768   Data           2  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    [Anonymous Symbol]                       0x00002768   Section        0  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    gPWM_0ClockConfig                        0x0000276a   Data           3  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    [Anonymous Symbol]                       0x0000276a   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    gPWM_0Config                             0x00002770   Data           8  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    [Anonymous Symbol]                       0x00002770   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    gSPI_LCD_clockConfig                     0x00002778   Data           2  ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig)
    [Anonymous Symbol]                       0x00002778   Section        0  ti_msp_dl_config.o(.rodata.gSPI_LCD_clockConfig)
    gSPI_LCD_config                          0x0000277a   Data          10  ti_msp_dl_config.o(.rodata.gSPI_LCD_config)
    [Anonymous Symbol]                       0x0000277a   Section        0  ti_msp_dl_config.o(.rodata.gSPI_LCD_config)
    gTIMER_TICKClockConfig                   0x00002784   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig)
    [Anonymous Symbol]                       0x00002784   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig)
    gTIMER_TICKTimerConfig                   0x00002788   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig)
    [Anonymous Symbol]                       0x00002788   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig)
    gUART_0ClockConfig                       0x0000279c   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x0000279c   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x0000279e   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x0000279e   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    gUART_2ClockConfig                       0x000027a8   Data           2  ti_msp_dl_config.o(.rodata.gUART_2ClockConfig)
    [Anonymous Symbol]                       0x000027a8   Section        0  ti_msp_dl_config.o(.rodata.gUART_2ClockConfig)
    gUART_2Config                            0x000027aa   Data          10  ti_msp_dl_config.o(.rodata.gUART_2Config)
    [Anonymous Symbol]                       0x000027aa   Section        0  ti_msp_dl_config.o(.rodata.gUART_2Config)
    gUART_WITClockConfig                     0x000027b4   Data           2  ti_msp_dl_config.o(.rodata.gUART_WITClockConfig)
    [Anonymous Symbol]                       0x000027b4   Section        0  ti_msp_dl_config.o(.rodata.gUART_WITClockConfig)
    gUART_WITConfig                          0x000027b6   Data          10  ti_msp_dl_config.o(.rodata.gUART_WITConfig)
    [Anonymous Symbol]                       0x000027b6   Section        0  ti_msp_dl_config.o(.rodata.gUART_WITConfig)
    .bss                                     0x20200000   Section       96  libspace.o(.bss)
    Motor_Receive_Data.RxCounter1            0x20200062   Data           1  zdt.o(.bss.Motor_Receive_Data.RxCounter1)
    [Anonymous Symbol]                       0x20200062   Section        0  zdt.o(.bss.Motor_Receive_Data.RxCounter1)
    Motor_Receive_Data.RxState               0x20200063   Data           1  zdt.o(.bss.Motor_Receive_Data.RxState)
    [Anonymous Symbol]                       0x20200063   Section        0  zdt.o(.bss.Motor_Receive_Data.RxState)
    Heap_Mem                                 0x20200250   Data         256  startup_mspm0g350x_uvision.o(HEAP)
    HEAP                                     0x20200250   Section      256  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x20200350   Data         768  startup_mspm0g350x_uvision.o(STACK)
    STACK                                    0x20200350   Section      768  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x20200650   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x00000121   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000129   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x00000145   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x00000147   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x00000149   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0000014b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0000014d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0000014d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0000014d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x00000153   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x00000153   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000157   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000157   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0000015f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x00000161   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x00000161   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x00000165   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x0000016d   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x00000171   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x00000173   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x00000175   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x00000177   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x00000179   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x0000017b   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x0000017b   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x0000017d   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    __use_two_region_memory                  0x0000019d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0000019f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x000001a1   Thumb Code     2  heapauxi.o(.text)
    __aeabi_d2f                              0x000001a5   Thumb Code     0  d2f.o(.text)
    __truncdfsf2                             0x000001a5   Thumb Code     0  d2f.o(.text)
    _d2f                                     0x000001a5   Thumb Code   120  d2f.o(.text)
    __aeabi_ddiv                             0x00000221   Thumb Code     0  ddiv.o(.text)
    __divdf3                                 0x00000221   Thumb Code     0  ddiv.o(.text)
    _ddiv                                    0x00000221   Thumb Code  1072  ddiv.o(.text)
    _drdiv                                   0x00000651   Thumb Code    20  ddiv.o(.text)
    __aeabi_d2iz                             0x00000669   Thumb Code     0  dfixi.o(.text)
    _dfix                                    0x00000669   Thumb Code    98  dfixi.o(.text)
    __aeabi_i2d_normalise                    0x000006d5   Thumb Code    66  dflti.o(.text)
    __aeabi_i2d                              0x00000717   Thumb Code    16  dflti.o(.text)
    _dflt                                    0x00000717   Thumb Code     0  dflti.o(.text)
    __aeabi_ui2d                             0x00000727   Thumb Code     6  dflti.o(.text)
    _dfltu                                   0x00000727   Thumb Code     0  dflti.o(.text)
    __aeabi_dmul                             0x0000072d   Thumb Code     0  dmul.o(.text)
    __muldf3                                 0x0000072d   Thumb Code     0  dmul.o(.text)
    _dmul                                    0x0000072d   Thumb Code   558  dmul.o(.text)
    __user_libspace                          0x00000975   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x00000975   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x00000975   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0000097d   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x000009bb   Thumb Code    16  exit.o(.text)
    DL_Common_delayCycles                    0x000009cb   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_DMA_initChannel                       0x00000a69   Thumb Code    68  dl_dma.o(.text.DL_DMA_initChannel)
    DL_I2C_setClockConfig                    0x00000d9f   Thumb Code    38  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_SPI_init                              0x00000e59   Thumb Code    68  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_setClockConfig                    0x00000ed5   Thumb Code    18  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_Timer_initFourCCPWMMode               0x00000ff5   Thumb Code   264  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_initTimerMode                   0x000010fd   Thumb Code   236  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00001215   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00001231   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00001249   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00001259   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_drainRXFIFO                      0x000012b1   Thumb Code    40  dl_uart.o(.text.DL_UART_drainRXFIFO)
    DL_UART_init                             0x00001369   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00001479   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    GROUP1_IRQHandler                        0x00001509   Thumb Code   268  encoder.o(.text.GROUP1_IRQHandler)
    Motor_0                                  0x00001621   Thumb Code    56  zdt.o(.text.Motor_0)
    Motor_Enable                             0x00001659   Thumb Code    60  zdt.o(.text.Motor_Enable)
    Motor_Enable_All                         0x00001695   Thumb Code    34  zdt.o(.text.Motor_Enable_All)
    Motor_Init                               0x000016b7   Thumb Code    48  zdt.o(.text.Motor_Init)
    Motor_Receive_Data                       0x000016e9   Thumb Code   360  zdt.o(.text.Motor_Receive_Data)
    Motor_Run                                0x00001865   Thumb Code    56  zdt.o(.text.Motor_Run)
    Motor_SetPosition                        0x0000189d   Thumb Code   276  zdt.o(.text.Motor_SetPosition)
    My_ABS                                   0x000019b9   Thumb Code    32  zdt.o(.text.My_ABS)
    SYSCFG_DL_DMA_WIT_init                   0x000019d9   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_WIT_init)
    SYSCFG_DL_DMA_init                       0x000019f1   Thumb Code     8  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    SYSCFG_DL_GPIO_init                      0x000019f9   Thumb Code   596  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_I2C_0_init                     0x00001c61   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    SYSCFG_DL_PWM_0_init                     0x00001cb5   Thumb Code   132  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    SYSCFG_DL_SPI_LCD_init                   0x00001d41   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_LCD_init)
    SYSCFG_DL_SYSCTL_init                    0x00001d7d   Thumb Code    42  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00001da7   Thumb Code    14  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_TICK_init                0x00001db5   Thumb Code    48  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    SYSCFG_DL_UART_0_init                    0x00001ded   Thumb Code    68  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_UART_2_init                    0x00001e39   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    SYSCFG_DL_UART_WIT_init                  0x00001e7d   Thumb Code    92  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_WIT_init)
    SYSCFG_DL_init                           0x00001ee1   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00001f35   Thumb Code   140  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    Stop_Flag_Clear                          0x00001fdd   Thumb Code    16  zdt.o(.text.Stop_Flag_Clear)
    UART0_IRQHandler                         0x00001ff9   Thumb Code    88  board.o(.text.UART0_IRQHandler)
    UART2_IRQHandler                         0x00002061   Thumb Code    44  board.o(.text.UART2_IRQHandler)
    UART3_IRQHandler                         0x00002091   Thumb Code   836  wit.o(.text.UART3_IRQHandler)
    UART_sendArray                           0x000023f9   Thumb Code    84  board.o(.text.UART_sendArray)
    _sys_exit                                0x00002579   Thumb Code     8  board.o(.text._sys_exit)
    board_init                               0x00002581   Thumb Code    26  board.o(.text.board_init)
    delay_ms                                 0x0000259b   Thumb Code    22  board.o(.text.delay_ms)
    delay_us                                 0x000025b1   Thumb Code   108  board.o(.text.delay_us)
    main                                     0x00002625   Thumb Code   124  empty.o(.text.main)
    uart0_send_char                          0x000026a5   Thumb Code    40  board.o(.text.uart0_send_char)
    __I$use$fp                               0x000026d0   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x000027c0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x000027d0   Number         0  anon$$obj.o(Region$$Table)
    __libspace_start                         0x20200000   Data          96  libspace.o(.bss)
    Motor01_Ready                            0x20200060   Data           1  zdt.o(.bss.Motor01_Ready)
    __temporary_stack_top$libspace           0x20200060   Data           0  libspace.o(.bss)
    Motor02_Ready                            0x20200061   Data           1  zdt.o(.bss.Motor02_Ready)
    RxBuffer2                                0x20200064   Data          10  zdt.o(.bss.RxBuffer2)
    Stop_Flag_Car                            0x2020006e   Data           1  zdt.o(.bss.Stop_Flag_Car)
    gPWM_0Backup                             0x20200070   Data         188  ti_msp_dl_config.o(.bss.gPWM_0Backup)
    gSPI_LCDBackup                           0x2020012c   Data          40  ti_msp_dl_config.o(.bss.gSPI_LCDBackup)
    gUART_WITBackup                          0x20200154   Data          48  ti_msp_dl_config.o(.bss.gUART_WITBackup)
    left_counter                             0x20200184   Data           2  encoder.o(.bss.left_counter)
    recv0_buff                               0x20200186   Data         128  board.o(.bss.recv0_buff)
    recv0_flag                               0x20200206   Data           1  board.o(.bss.recv0_flag)
    recv0_length                             0x20200208   Data           2  board.o(.bss.recv0_length)
    right_counter                            0x2020020a   Data           2  encoder.o(.bss.right_counter)
    wit_data                                 0x2020020c   Data          32  wit.o(.bss.wit_data)
    wit_dmaBuffer                            0x2020022c   Data          33  wit.o(.bss.wit_dmaBuffer)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x000027d0, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x000027d0, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           35    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO         1149  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO         1640    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x00000002   Code   RO         1641    !!handler_null      c_p.l(__scatter.o)
    0x00000122   0x00000122   0x00000006   PAD
    0x00000128   0x00000128   0x0000001c   Code   RO         1644    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000144   0x00000144   0x00000002   Code   RO         1544    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1349    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1351    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1353    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1356    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1358    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1360    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1363    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1365    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1367    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1369    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1371    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1373    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1375    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1377    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1379    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1381    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1383    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1387    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1389    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1391    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000000   Code   RO         1393    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000146   0x00000146   0x00000002   Code   RO         1394    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000148   0x00000148   0x00000002   Code   RO         1607    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1623    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1625    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1628    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1631    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1633    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000000   Code   RO         1636    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0000014a   0x0000014a   0x00000002   Code   RO         1637    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x0000014c   0x0000014c   0x00000000   Code   RO         1246    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x0000014c   0x0000014c   0x00000000   Code   RO         1445    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x0000014c   0x0000014c   0x00000006   Code   RO         1457    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x00000152   0x00000152   0x00000000   Code   RO         1447    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x00000152   0x00000152   0x00000004   Code   RO         1448    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000156   0x00000156   0x00000000   Code   RO         1450    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000156   0x00000156   0x00000008   Code   RO         1451    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x0000015e   0x0000015e   0x00000002   Code   RO         1558    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x00000160   0x00000160   0x00000000   Code   RO         1585    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x00000160   0x00000160   0x00000004   Code   RO         1586    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x00000164   0x00000164   0x00000006   Code   RO         1587    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0000016a   0x0000016a   0x00000002   PAD
    0x0000016c   0x0000016c   0x00000030   Code   RO           36    .text               startup_mspm0g350x_uvision.o
    0x0000019c   0x0000019c   0x00000006   Code   RO         1147    .text               c_p.l(heapauxi.o)
    0x000001a2   0x000001a2   0x00000002   PAD
    0x000001a4   0x000001a4   0x0000007c   Code   RO         1151    .text               fz_ps.l(d2f.o)
    0x00000220   0x00000220   0x00000448   Code   RO         1179    .text               fz_ps.l(ddiv.o)
    0x00000668   0x00000668   0x0000006c   Code   RO         1182    .text               fz_ps.l(dfixi.o)
    0x000006d4   0x000006d4   0x00000058   Code   RO         1186    .text               fz_ps.l(dflti.o)
    0x0000072c   0x0000072c   0x00000248   Code   RO         1188    .text               fz_ps.l(dmul.o)
    0x00000974   0x00000974   0x00000008   Code   RO         1500    .text               c_p.l(libspace.o)
    0x0000097c   0x0000097c   0x0000003e   Code   RO         1503    .text               c_p.l(sys_stackheap_outer.o)
    0x000009ba   0x000009ba   0x00000010   Code   RO         1529    .text               c_p.l(exit.o)
    0x000009ca   0x000009ca   0x0000000a   Code   RO          877    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x000009d4   0x000009d4   0x00000028   Code   RO          185    .text.DL_Common_updateReg  ti_msp_dl_config.o
    0x000009fc   0x000009fc   0x00000026   Code   RO          860    .text.DL_DMA_disableChannel  wit.o
    0x00000a22   0x00000a22   0x00000026   Code   RO          854    .text.DL_DMA_enableChannel  wit.o
    0x00000a48   0x00000a48   0x00000020   Code   RO          862    .text.DL_DMA_getTransferSize  wit.o
    0x00000a68   0x00000a68   0x00000044   Code   RO          886    .text.DL_DMA_initChannel  driverlib.a(dl_dma.o)
    0x00000aac   0x00000aac   0x00000028   Code   RO          850    .text.DL_DMA_setDestAddr  wit.o
    0x00000ad4   0x00000ad4   0x00000030   Code   RO          852    .text.DL_DMA_setTransferSize  wit.o
    0x00000b04   0x00000b04   0x00000018   Code   RO          117    .text.DL_GPIO_clearInterruptStatus  ti_msp_dl_config.o
    0x00000b1c   0x00000b1c   0x0000001c   Code   RO          254    .text.DL_GPIO_clearInterruptStatus  encoder.o
    0x00000b38   0x00000b38   0x00000014   Code   RO          109    .text.DL_GPIO_clearPins  ti_msp_dl_config.o
    0x00000b4c   0x00000b4c   0x00000018   Code   RO          101    .text.DL_GPIO_enableHiZ  ti_msp_dl_config.o
    0x00000b64   0x00000b64   0x0000001c   Code   RO          119    .text.DL_GPIO_enableInterrupt  ti_msp_dl_config.o
    0x00000b80   0x00000b80   0x00000018   Code   RO           97    .text.DL_GPIO_enableOutput  ti_msp_dl_config.o
    0x00000b98   0x00000b98   0x00000014   Code   RO           83    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x00000bac   0x00000bac   0x00000018   Code   RO          250    .text.DL_GPIO_getEnabledInterruptStatus  encoder.o
    0x00000bc4   0x00000bc4   0x00000030   Code   RO          107    .text.DL_GPIO_initDigitalInputFeatures  ti_msp_dl_config.o
    0x00000bf4   0x00000bf4   0x00000014   Code   RO          105    .text.DL_GPIO_initDigitalOutput  ti_msp_dl_config.o
    0x00000c08   0x00000c08   0x00000014   Code   RO           93    .text.DL_GPIO_initPeripheralAnalogFunction  ti_msp_dl_config.o
    0x00000c1c   0x00000c1c   0x00000018   Code   RO          103    .text.DL_GPIO_initPeripheralInputFunction  ti_msp_dl_config.o
    0x00000c34   0x00000c34   0x00000038   Code   RO           99    .text.DL_GPIO_initPeripheralInputFunctionFeatures  ti_msp_dl_config.o
    0x00000c6c   0x00000c6c   0x0000001c   Code   RO           95    .text.DL_GPIO_initPeripheralOutputFunction  ti_msp_dl_config.o
    0x00000c88   0x00000c88   0x00000016   Code   RO          252    .text.DL_GPIO_readPins  encoder.o
    0x00000c9e   0x00000c9e   0x00000002   PAD
    0x00000ca0   0x00000ca0   0x00000010   Code   RO           73    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x00000cb0   0x00000cb0   0x0000001c   Code   RO          113    .text.DL_GPIO_setLowerPinsPolarity  ti_msp_dl_config.o
    0x00000ccc   0x00000ccc   0x00000018   Code   RO          111    .text.DL_GPIO_setPins  ti_msp_dl_config.o
    0x00000ce4   0x00000ce4   0x00000018   Code   RO          115    .text.DL_GPIO_setUpperPinsPolarity  ti_msp_dl_config.o
    0x00000cfc   0x00000cfc   0x00000018   Code   RO          143    .text.DL_I2C_enableAnalogGlitchFilter  ti_msp_dl_config.o
    0x00000d14   0x00000d14   0x00000014   Code   RO          155    .text.DL_I2C_enableController  ti_msp_dl_config.o
    0x00000d28   0x00000d28   0x00000018   Code   RO          153    .text.DL_I2C_enableControllerClockStretching  ti_msp_dl_config.o
    0x00000d40   0x00000d40   0x00000014   Code   RO           87    .text.DL_I2C_enablePower  ti_msp_dl_config.o
    0x00000d54   0x00000d54   0x00000010   Code   RO           77    .text.DL_I2C_reset  ti_msp_dl_config.o
    0x00000d64   0x00000d64   0x00000014   Code   RO          145    .text.DL_I2C_resetControllerTransfer  ti_msp_dl_config.o
    0x00000d78   0x00000d78   0x00000026   Code   RO          141    .text.DL_I2C_setAnalogGlitchFilterPulseWidth  ti_msp_dl_config.o
    0x00000d9e   0x00000d9e   0x00000026   Code   RO          896    .text.DL_I2C_setClockConfig  driverlib.a(dl_i2c.o)
    0x00000dc4   0x00000dc4   0x00000024   Code   RO          151    .text.DL_I2C_setControllerRXFIFOThreshold  ti_msp_dl_config.o
    0x00000de8   0x00000de8   0x00000028   Code   RO          149    .text.DL_I2C_setControllerTXFIFOThreshold  ti_msp_dl_config.o
    0x00000e10   0x00000e10   0x00000018   Code   RO          147    .text.DL_I2C_setTimerPeriod  ti_msp_dl_config.o
    0x00000e28   0x00000e28   0x00000018   Code   RO          177    .text.DL_SPI_enable  ti_msp_dl_config.o
    0x00000e40   0x00000e40   0x00000018   Code   RO           91    .text.DL_SPI_enablePower  ti_msp_dl_config.o
    0x00000e58   0x00000e58   0x00000044   Code   RO          928    .text.DL_SPI_init   driverlib.a(dl_spi.o)
    0x00000e9c   0x00000e9c   0x00000018   Code   RO           81    .text.DL_SPI_reset  ti_msp_dl_config.o
    0x00000eb4   0x00000eb4   0x00000020   Code   RO          173    .text.DL_SPI_setBitRateSerialClockDivider  ti_msp_dl_config.o
    0x00000ed4   0x00000ed4   0x00000012   Code   RO          930    .text.DL_SPI_setClockConfig  driverlib.a(dl_spi.o)
    0x00000ee6   0x00000ee6   0x00000002   PAD
    0x00000ee8   0x00000ee8   0x0000002c   Code   RO          175    .text.DL_SPI_setFIFOThreshold  ti_msp_dl_config.o
    0x00000f14   0x00000f14   0x0000000c   Code   RO          125    .text.DL_SYSCTL_disableHFXT  ti_msp_dl_config.o
    0x00000f20   0x00000f20   0x00000014   Code   RO          127    .text.DL_SYSCTL_disableSYSPLL  ti_msp_dl_config.o
    0x00000f34   0x00000f34   0x00000014   Code   RO          129    .text.DL_SYSCTL_enableMFCLK  ti_msp_dl_config.o
    0x00000f48   0x00000f48   0x00000018   Code   RO          121    .text.DL_SYSCTL_setBORThreshold  ti_msp_dl_config.o
    0x00000f60   0x00000f60   0x0000001c   Code   RO          123    .text.DL_SYSCTL_setSYSOSCFreq  ti_msp_dl_config.o
    0x00000f7c   0x00000f7c   0x0000000c   Code   RO          183    .text.DL_SYSTICK_enable  ti_msp_dl_config.o
    0x00000f88   0x00000f88   0x00000028   Code   RO          181    .text.DL_SYSTICK_init  ti_msp_dl_config.o
    0x00000fb0   0x00000fb0   0x00000014   Code   RO          135    .text.DL_Timer_enableClock  ti_msp_dl_config.o
    0x00000fc4   0x00000fc4   0x00000018   Code   RO          139    .text.DL_Timer_enableInterrupt  ti_msp_dl_config.o
    0x00000fdc   0x00000fdc   0x00000018   Code   RO           85    .text.DL_Timer_enablePower  ti_msp_dl_config.o
    0x00000ff4   0x00000ff4   0x00000108   Code   RO         1062    .text.DL_Timer_initFourCCPWMMode  driverlib.a(dl_timer.o)
    0x000010fc   0x000010fc   0x000000ec   Code   RO          986    .text.DL_Timer_initTimerMode  driverlib.a(dl_timer.o)
    0x000011e8   0x000011e8   0x00000018   Code   RO           75    .text.DL_Timer_reset  ti_msp_dl_config.o
    0x00001200   0x00001200   0x00000014   Code   RO          137    .text.DL_Timer_setCCPDirection  ti_msp_dl_config.o
    0x00001214   0x00001214   0x0000001c   Code   RO         1028    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x00001230   0x00001230   0x00000018   Code   RO         1036    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00001248   0x00001248   0x00000010   Code   RO          988    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00001258   0x00001258   0x0000001c   Code   RO          982    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x00001274   0x00001274   0x0000003c   Code   RO          133    .text.DL_Timer_setCounterControl  ti_msp_dl_config.o
    0x000012b0   0x000012b0   0x00000028   Code   RO         1103    .text.DL_UART_drainRXFIFO  driverlib.a(dl_uart.o)
    0x000012d8   0x000012d8   0x00000016   Code   RO          163    .text.DL_UART_enable  ti_msp_dl_config.o
    0x000012ee   0x000012ee   0x00000002   PAD
    0x000012f0   0x000012f0   0x00000018   Code   RO          165    .text.DL_UART_enableDMAReceiveEvent  ti_msp_dl_config.o
    0x00001308   0x00001308   0x00000018   Code   RO          167    .text.DL_UART_enableFIFOs  ti_msp_dl_config.o
    0x00001320   0x00001320   0x0000001c   Code   RO          161    .text.DL_UART_enableInterrupt  ti_msp_dl_config.o
    0x0000133c   0x0000133c   0x00000018   Code   RO           89    .text.DL_UART_enablePower  ti_msp_dl_config.o
    0x00001354   0x00001354   0x00000012   Code   RO          497    .text.DL_UART_getPendingInterrupt  board.o
    0x00001366   0x00001366   0x00000002   PAD
    0x00001368   0x00001368   0x00000048   Code   RO         1083    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x000013b0   0x000013b0   0x00000014   Code   RO          481    .text.DL_UART_isBusy  board.o
    0x000013c4   0x000013c4   0x00000014   Code   RO          864    .text.DL_UART_isRXFIFOEmpty  wit.o
    0x000013d8   0x000013d8   0x00000018   Code   RO          489    .text.DL_UART_isTXFIFOFull  board.o
    0x000013f0   0x000013f0   0x00000010   Code   RO          499    .text.DL_UART_receiveData  board.o
    0x00001400   0x00001400   0x00000014   Code   RO          866    .text.DL_UART_receiveData  wit.o
    0x00001414   0x00001414   0x00000018   Code   RO           79    .text.DL_UART_reset  ti_msp_dl_config.o
    0x0000142c   0x0000142c   0x0000004c   Code   RO          159    .text.DL_UART_setBaudRateDivisor  ti_msp_dl_config.o
    0x00001478   0x00001478   0x00000012   Code   RO         1085    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x0000148a   0x0000148a   0x0000001e   Code   RO          157    .text.DL_UART_setOversampling  ti_msp_dl_config.o
    0x000014a8   0x000014a8   0x00000024   Code   RO          169    .text.DL_UART_setRXFIFOThreshold  ti_msp_dl_config.o
    0x000014cc   0x000014cc   0x00000024   Code   RO          171    .text.DL_UART_setRXInterruptTimeout  ti_msp_dl_config.o
    0x000014f0   0x000014f0   0x00000016   Code   RO          483    .text.DL_UART_transmitData  board.o
    0x00001506   0x00001506   0x00000002   PAD
    0x00001508   0x00001508   0x00000118   Code   RO          248    .text.GROUP1_IRQHandler  encoder.o
    0x00001620   0x00001620   0x00000038   Code   RO          400    .text.Motor_0       zdt.o
    0x00001658   0x00001658   0x0000003c   Code   RO          404    .text.Motor_Enable  zdt.o
    0x00001694   0x00001694   0x00000022   Code   RO          418    .text.Motor_Enable_All  zdt.o
    0x000016b6   0x000016b6   0x00000030   Code   RO          412    .text.Motor_Init    zdt.o
    0x000016e6   0x000016e6   0x00000002   PAD
    0x000016e8   0x000016e8   0x0000017c   Code   RO          398    .text.Motor_Receive_Data  zdt.o
    0x00001864   0x00001864   0x00000038   Code   RO          426    .text.Motor_Run     zdt.o
    0x0000189c   0x0000189c   0x0000011c   Code   RO          430    .text.Motor_SetPosition  zdt.o
    0x000019b8   0x000019b8   0x00000020   Code   RO          394    .text.My_ABS        zdt.o
    0x000019d8   0x000019d8   0x00000018   Code   RO          179    .text.SYSCFG_DL_DMA_WIT_init  ti_msp_dl_config.o
    0x000019f0   0x000019f0   0x00000008   Code   RO           65    .text.SYSCFG_DL_DMA_init  ti_msp_dl_config.o
    0x000019f8   0x000019f8   0x00000268   Code   RO           47    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00001c60   0x00001c60   0x00000054   Code   RO           55    .text.SYSCFG_DL_I2C_0_init  ti_msp_dl_config.o
    0x00001cb4   0x00001cb4   0x0000008c   Code   RO           51    .text.SYSCFG_DL_PWM_0_init  ti_msp_dl_config.o
    0x00001d40   0x00001d40   0x0000003c   Code   RO           63    .text.SYSCFG_DL_SPI_LCD_init  ti_msp_dl_config.o
    0x00001d7c   0x00001d7c   0x0000002a   Code   RO           49    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00001da6   0x00001da6   0x0000000e   Code   RO           67    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00001db4   0x00001db4   0x00000038   Code   RO           53    .text.SYSCFG_DL_TIMER_TICK_init  ti_msp_dl_config.o
    0x00001dec   0x00001dec   0x0000004c   Code   RO           57    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00001e38   0x00001e38   0x00000044   Code   RO           61    .text.SYSCFG_DL_UART_2_init  ti_msp_dl_config.o
    0x00001e7c   0x00001e7c   0x00000064   Code   RO           59    .text.SYSCFG_DL_UART_WIT_init  ti_msp_dl_config.o
    0x00001ee0   0x00001ee0   0x00000054   Code   RO           43    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00001f34   0x00001f34   0x000000a8   Code   RO           45    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00001fdc   0x00001fdc   0x0000001c   Code   RO          396    .text.Stop_Flag_Clear  zdt.o
    0x00001ff8   0x00001ff8   0x00000068   Code   RO          495    .text.UART0_IRQHandler  board.o
    0x00002060   0x00002060   0x00000030   Code   RO          501    .text.UART2_IRQHandler  board.o
    0x00002090   0x00002090   0x00000368   Code   RO          858    .text.UART3_IRQHandler  wit.o
    0x000023f8   0x000023f8   0x00000054   Code   RO          487    .text.UART_sendArray  board.o
    0x0000244c   0x0000244c   0x00000028   Code   RO          414    .text.__NVIC_ClearPendingIRQ  zdt.o
    0x00002474   0x00002474   0x0000002c   Code   RO          467    .text.__NVIC_ClearPendingIRQ  board.o
    0x000024a0   0x000024a0   0x00000028   Code   RO          416    .text.__NVIC_EnableIRQ  zdt.o
    0x000024c8   0x000024c8   0x0000002c   Code   RO          469    .text.__NVIC_EnableIRQ  board.o
    0x000024f4   0x000024f4   0x00000084   Code   RO          131    .text.__NVIC_SetPriority  ti_msp_dl_config.o
    0x00002578   0x00002578   0x00000008   Code   RO          491    .text._sys_exit     board.o
    0x00002580   0x00002580   0x0000001a   Code   RO          465    .text.board_init    board.o
    0x0000259a   0x0000259a   0x00000016   Code   RO          473    .text.delay_ms      board.o
    0x000025b0   0x000025b0   0x00000074   Code   RO          471    .text.delay_us      board.o
    0x00002624   0x00002624   0x00000080   Code   RO            2    .text.main          empty.o
    0x000026a4   0x000026a4   0x0000002c   Code   RO          479    .text.uart0_send_char  board.o
    0x000026d0   0x000026d0   0x00000000   Code   RO         1419    x$fpl$usenofp       fz_ps.l(usenofp.o)
    0x000026d0   0x000026d0   0x00000080   Data   RO         1180    .constdata          fz_ps.l(ddiv.o)
    0x00002750   0x00002750   0x00000018   Data   RO          203    .rodata.gDMA_WITConfig  ti_msp_dl_config.o
    0x00002768   0x00002768   0x00000002   Data   RO          194    .rodata.gI2C_0ClockConfig  ti_msp_dl_config.o
    0x0000276a   0x0000276a   0x00000003   Data   RO          190    .rodata.gPWM_0ClockConfig  ti_msp_dl_config.o
    0x0000276d   0x0000276d   0x00000003   PAD
    0x00002770   0x00002770   0x00000008   Data   RO          191    .rodata.gPWM_0Config  ti_msp_dl_config.o
    0x00002778   0x00002778   0x00000002   Data   RO          201    .rodata.gSPI_LCD_clockConfig  ti_msp_dl_config.o
    0x0000277a   0x0000277a   0x0000000a   Data   RO          202    .rodata.gSPI_LCD_config  ti_msp_dl_config.o
    0x00002784   0x00002784   0x00000003   Data   RO          192    .rodata.gTIMER_TICKClockConfig  ti_msp_dl_config.o
    0x00002787   0x00002787   0x00000001   PAD
    0x00002788   0x00002788   0x00000014   Data   RO          193    .rodata.gTIMER_TICKTimerConfig  ti_msp_dl_config.o
    0x0000279c   0x0000279c   0x00000002   Data   RO          195    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x0000279e   0x0000279e   0x0000000a   Data   RO          196    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x000027a8   0x000027a8   0x00000002   Data   RO          199    .rodata.gUART_2ClockConfig  ti_msp_dl_config.o
    0x000027aa   0x000027aa   0x0000000a   Data   RO          200    .rodata.gUART_2Config  ti_msp_dl_config.o
    0x000027b4   0x000027b4   0x00000002   Data   RO          197    .rodata.gUART_WITClockConfig  ti_msp_dl_config.o
    0x000027b6   0x000027b6   0x0000000a   Data   RO          198    .rodata.gUART_WITConfig  ti_msp_dl_config.o
    0x000027c0   0x000027c0   0x00000010   Data   RO         1639    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x000027d0, Size: 0x00000650, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000        -       0x00000060   Zero   RW         1501    .bss                c_p.l(libspace.o)
    0x20200060        -       0x00000001   Zero   RW          442    .bss.Motor01_Ready  zdt.o
    0x20200061        -       0x00000001   Zero   RW          443    .bss.Motor02_Ready  zdt.o
    0x20200062        -       0x00000001   Zero   RW          451    .bss.Motor_Receive_Data.RxCounter1  zdt.o
    0x20200063        -       0x00000001   Zero   RW          452    .bss.Motor_Receive_Data.RxState  zdt.o
    0x20200064        -       0x0000000a   Zero   RW          453    .bss.RxBuffer2      zdt.o
    0x2020006e        -       0x00000001   Zero   RW          450    .bss.Stop_Flag_Car  zdt.o
    0x2020006f   0x000027d0   0x00000001   PAD
    0x20200070        -       0x000000bc   Zero   RW          187    .bss.gPWM_0Backup   ti_msp_dl_config.o
    0x2020012c        -       0x00000028   Zero   RW          189    .bss.gSPI_LCDBackup  ti_msp_dl_config.o
    0x20200154        -       0x00000030   Zero   RW          188    .bss.gUART_WITBackup  ti_msp_dl_config.o
    0x20200184        -       0x00000002   Zero   RW          260    .bss.left_counter   encoder.o
    0x20200186        -       0x00000080   Zero   RW          503    .bss.recv0_buff     board.o
    0x20200206        -       0x00000001   Zero   RW          505    .bss.recv0_flag     board.o
    0x20200207   0x000027d0   0x00000001   PAD
    0x20200208        -       0x00000002   Zero   RW          504    .bss.recv0_length   board.o
    0x2020020a        -       0x00000002   Zero   RW          261    .bss.right_counter  encoder.o
    0x2020020c        -       0x00000020   Zero   RW          869    .bss.wit_data       wit.o
    0x2020022c        -       0x00000021   Zero   RW          868    .bss.wit_dmaBuffer  wit.o
    0x2020024d   0x000027d0   0x00000003   PAD
    0x20200250        -       0x00000100   Zero   RW           34    HEAP                startup_mspm0g350x_uvision.o
    0x20200350        -       0x00000300   Zero   RW           33    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x000000ff, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       640         44          0          0        131       7749   board.o
       128          4          0          0          0       4430   empty.o
       354         20          0          0          4       6115   encoder.o
        48         22        192          0       1024        692   startup_mspm0g350x_uvision.o
      3202        284        108          0        276      38015   ti_msp_dl_config.o
      1108         48          0          0         65       7446   wit.o
      1058         40          0          0         15      11551   zdt.o

    ----------------------------------------------------------------------
      6548        <USER>        <GROUP>          0       1520      75998   Object Totals
         0          0         16          0          0          0   (incl. Generated)
        10          0          4          0          5          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        62          0          0          0          0         80   sys_stackheap_outer.o
        10          0          0          0          0        803   dl_common.o
        68          4          0          0          0       4510   dl_dma.o
        38          0          0          0          0       8620   dl_i2c.o
        86          8          0          0          0      13518   dl_spi.o
       596        188          0          0          0      41557   dl_timer.o
       130         12          0          0          0      14163   dl_uart.o
       124          4          0          0          0         72   d2f.o
      1096         26        128          0          0        112   ddiv.o
       108         10          0          0          0         72   dfixi.o
        88          0          0          0          0         92   dflti.o
       584         26          0          0          0         84   dmul.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      3196        <USER>        <GROUP>          0         96      84023   Library Totals
        16          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       252         14          0          0         96        420   c_p.l
       928        212          0          0          0      83171   driverlib.a
      2000         66        128          0          0        432   fz_ps.l

    ----------------------------------------------------------------------
      3196        <USER>        <GROUP>          0         96      84023   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9744        758        448          0       1616     159585   Grand Totals
      9744        758        448          0       1616     159585   ELF Image Totals
      9744        758        448          0          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                10192 (   9.95kB)
    Total RW  Size (RW Data + ZI Data)              1616 (   1.58kB)
    Total ROM Size (Code + RO Data + RW Data)      10192 (   9.95kB)

==============================================================================

