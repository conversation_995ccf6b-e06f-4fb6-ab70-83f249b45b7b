//right_distance
// Created by faz<PERSON><PERSON> on 2024/7/24.
//
// Created by faz<PERSON><PERSON> on 2024/7/24.
//

#ifndef ENCODER_H
#define ENCODER_H


#include "ti_msp_dl_config.h"
#include "empty.h"

//extern volatile int16_t left_counter;
//extern volatile int16_t right_counter;

extern int16_t left_speed;
extern int32_t left_distance;
extern int16_t right_speed;
extern int32_t right_distance;

void encoder_init();
void encoder_callback();
void timer_init(void);

#endif //ENCODER_H
