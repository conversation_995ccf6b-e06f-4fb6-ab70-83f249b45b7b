#ifndef __XUNJI_H__
#define __XUNJI_H__

#include "empty.h"

//GPIO_getInputPinValue(GPIO_PORT_P1, GPIO_PIN4)
#define L6  my_GPIO_readPin(GPIO_XUNJI_PORT, GP<PERSON>_XUNJI_IO1_PIN)
#define L5  my_GPIO_readPin(GPIO_XUNJI_PORT, GPIO_XUNJI_IO2_PIN)
#define L4  my_GPIO_readPin(GPIO_XUNJI_PORT, GPIO_XUNJI_IO3_PIN)
#define L3  my_GPIO_readPin(GPIO_XUNJI_PORT, GPIO_XUNJI_IO4_PIN)
#define L2  my_GPIO_readPin(GPIO_XUNJI_PORT, GPIO_XUNJI_IO5_PIN)
#define L1  my_GPIO_readPin(GPIO_XUNJI_PORT, GPIO_XUNJI_IO6_PIN)
#define R1  my_GPIO_readPin(GPIO_XUNJI_PORT, GP<PERSON>_XUNJI_IO7_PIN)
#define R2  my_GPIO_readPin(GPIO_XUNJI_PORT, GP<PERSON>_XUNJI_IO8_PIN)
#define R3  my_GPIO_readPin(GPIO_XUNJI_PORT, GPIO_XUNJI_IO9_PIN)
#define R4  my_GPIO_readPin(GPIO_XUNJI_PORT, GPIO_XUNJI_IO10_PIN)
#define R5  my_GPIO_readPin(GPIO_XUNJI_PORT, GPIO_XUNJI_IO11_PIN)
#define R6  my_GPIO_readPin(GPIO_XUNJI_PORT, GPIO_XUNJI_IO12_PIN)
		
static uint16_t find_set_bit(uint32_t num);
uint8_t my_GPIO_readPin(GPIO_Regs *gpio, uint32_t pins);
void xunji();

#endif