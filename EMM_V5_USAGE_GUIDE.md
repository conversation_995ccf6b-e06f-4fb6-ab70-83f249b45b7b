# Emm_V5.0步进电机驱动器控制方案

## 1. 技术方案对比总结

### 1.1 原GPIO方案 vs Emm_V5.0 UART方案

| 对比项目 | 原GPIO方案 | Emm_V5.0 UART方案 |
|---------|-----------|------------------|
| **控制方式** | STEP/DIR/EN脉冲控制 | UART串口通信控制 |
| **接线数量** | 3根控制线 | 2根通信线 |
| **实时性** | 优秀 | 良好 |
| **功能丰富度** | 基础 | 丰富 |
| **调试便利性** | 一般 | 优秀 |
| **状态反馈** | 无 | 有 |
| **多机控制** | 困难 | 简单 |

### 1.2 为什么选择UART方案

1. **Emm_V5.0完全支持**脉冲控制，但UART方案功能更强大
2. **恒速转动更简单**：一条命令即可实现，无需复杂的脉冲生成
3. **状态监控**：可以读取电机转速、位置、电流等状态
4. **参数调整**：可以动态调整加速度、细分等参数
5. **错误处理**：支持堵转保护、到位检测等功能

## 2. 硬件连接方案

### 2.1 天猛星开发板连接

```
天猛星开发板 MSPM0G3507    Emm_V5.0驱动器
PA21 (UART2_TX) --------> R/A/H (RX)
PA22 (UART2_RX) --------> T/B/L (TX)  
GND             --------> GND
24V电源          --------> V+ (7-32V)
```

### 2.2 驱动器配置设置

通过驱动器小屏幕菜单设置：
```
P_Serial: UART_FUN     (串口通信模式)
UartBaud: 115200       (波特率)
ID_Addr: 1             (设备地址)
Checksum: 0x6B         (校验方式)
MotType: 1.8°          (电机类型)
MStep: 16              (细分数)
```

## 3. 软件使用方法

### 3.1 基本初始化

```c
#include "HARDWARE/stepper_motor.h"

int main(void)
{
    // 系统初始化
    board_init();
    
    // Emm_V5.0驱动器初始化
    EMM_V5_Init();
    delay_ms(100);
    
    // 使能步进电机
    EMM_V5_Enable(1);
    delay_ms(50);
    
    // 开始恒速转动
    EMM_V5_Start_Continuous_Rotation(EMM_DIR_CW, 60);  // 60RPM顺时针
    
    while(1) {
        delay_ms(100);
    }
}
```

### 3.2 核心API函数

#### 3.2.1 初始化和使能
```c
void EMM_V5_Init(void);                    // 初始化驱动器
void EMM_V5_Enable(uint8_t enable);        // 使能控制 (1=使能, 0=禁用)
```

#### 3.2.2 恒速转动 (核心功能)
```c
// 开始恒速连续转动
void EMM_V5_Start_Continuous_Rotation(emm_direction_t dir, uint16_t rpm);

// 使用示例
EMM_V5_Start_Continuous_Rotation(EMM_DIR_CW, 100);   // 100RPM顺时针
EMM_V5_Start_Continuous_Rotation(EMM_DIR_CCW, 200);  // 200RPM逆时针
```

#### 3.2.3 精确控制
```c
// 速度模式控制
void EMM_V5_Set_Speed_Mode(emm_direction_t dir, uint16_t rpm, uint8_t acceleration);

// 位置模式控制
void EMM_V5_Set_Position_Mode(emm_direction_t dir, uint16_t rpm, uint8_t acceleration, uint32_t pulses);

// 立即停止
void EMM_V5_Stop_Immediately(void);
```

### 3.3 使用示例

#### 3.3.1 简单恒速转动
```c
// 60RPM恒速顺时针转动
EMM_V5_Start_Continuous_Rotation(EMM_DIR_CW, 60);
```

#### 3.3.2 变速控制
```c
// 启动时慢速，然后加速到高速
EMM_V5_Set_Speed_Mode(EMM_DIR_CW, 30, 50);   // 30RPM，加速度50
delay_ms(2000);
EMM_V5_Set_Speed_Mode(EMM_DIR_CW, 200, 100); // 200RPM，加速度100
```

#### 3.3.3 定角度转动
```c
// 转动指定角度 (16细分下，3200脉冲 = 360度)
uint32_t pulses_per_360 = 3200;  // 16细分下的一圈脉冲数
uint32_t pulses_90_deg = pulses_per_360 / 4;  // 90度对应的脉冲数

EMM_V5_Set_Position_Mode(EMM_DIR_CW, 100, 50, pulses_90_deg);  // 转动90度
```

## 4. 高级功能

### 4.1 多机控制
```c
// 通过修改设备地址实现多机控制
emm_motor.device_addr = 1;  // 控制1号电机
EMM_V5_Start_Continuous_Rotation(EMM_DIR_CW, 100);

emm_motor.device_addr = 2;  // 控制2号电机  
EMM_V5_Start_Continuous_Rotation(EMM_DIR_CCW, 150);
```

### 4.2 状态监控
```c
// 可以通过扩展代码添加状态读取功能
// 读取电机转速、位置、电流等参数
```

## 5. 注意事项

### 5.1 硬件注意事项
1. **供电电压**：确保7-32V供电，推荐24V
2. **接线正确**：TX接RX，RX接TX
3. **共地连接**：确保开发板和驱动器共地

### 5.2 软件注意事项
1. **初始化顺序**：先初始化，再使能，最后控制
2. **延时设置**：命令间需要适当延时
3. **错误处理**：检查命令返回值，处理通信失败

### 5.3 调试建议
1. **串口监控**：可以用示波器或逻辑分析仪监控UART通信
2. **状态指示**：观察驱动器LED状态指示
3. **分步测试**：先测试使能，再测试转动

## 6. 性能参数

- **转速范围**：0.1 - 3000+ RPM
- **控制精度**：< 0.08°
- **最大电流**：3000mA
- **通信波特率**：115200 bps
- **响应时间**：< 10ms

这个方案充分利用了Emm_V5.0的强大功能，实现了从启动就保持恒定速度转动的需求，同时提供了丰富的扩展功能。
