/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define GPIO_HFXT_PORT                                                     GPIOA
#define GPIO_HFXIN_PIN                                             DL_GPIO_PIN_5
#define GPIO_HFXIN_IOMUX                                         (IOMUX_PINCM10)
#define GPIO_HFXOUT_PIN                                            DL_GPIO_PIN_6
#define GPIO_HFXOUT_IOMUX                                        (IOMUX_PINCM11)
#define CPUCLK_FREQ                                                     32000000



/* Defines for PWM_0 */
#define PWM_0_INST                                                         TIMA1
#define PWM_0_INST_IRQHandler                                   TIMA1_IRQHandler
#define PWM_0_INST_INT_IRQN                                     (TIMA1_INT_IRQn)
#define PWM_0_INST_CLK_FREQ                                              4000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_0_C0_PORT                                                 GPIOA
#define GPIO_PWM_0_C0_PIN                                         DL_GPIO_PIN_17
#define GPIO_PWM_0_C0_IOMUX                                      (IOMUX_PINCM39)
#define GPIO_PWM_0_C0_IOMUX_FUNC                     IOMUX_PINCM39_PF_TIMA1_CCP0
#define GPIO_PWM_0_C0_IDX                                    DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_0_C1_PORT                                                 GPIOA
#define GPIO_PWM_0_C1_PIN                                         DL_GPIO_PIN_16
#define GPIO_PWM_0_C1_IOMUX                                      (IOMUX_PINCM38)
#define GPIO_PWM_0_C1_IOMUX_FUNC                     IOMUX_PINCM38_PF_TIMA1_CCP1
#define GPIO_PWM_0_C1_IDX                                    DL_TIMER_CC_1_INDEX



/* Defines for TIMER_TICK */
#define TIMER_TICK_INST                                                  (TIMG0)
#define TIMER_TICK_INST_IRQHandler                              TIMG0_IRQHandler
#define TIMER_TICK_INST_INT_IRQN                                (TIMG0_INT_IRQn)
#define TIMER_TICK_INST_LOAD_VALUE                                        (399U)




/* Defines for I2C_0 */
#define I2C_0_INST                                                          I2C1
#define I2C_0_INST_IRQHandler                                    I2C1_IRQHandler
#define I2C_0_INST_INT_IRQN                                        I2C1_INT_IRQn
#define I2C_0_BUS_SPEED_HZ                                                400000
#define GPIO_I2C_0_SDA_PORT                                                GPIOB
#define GPIO_I2C_0_SDA_PIN                                         DL_GPIO_PIN_3
#define GPIO_I2C_0_IOMUX_SDA                                     (IOMUX_PINCM16)
#define GPIO_I2C_0_IOMUX_SDA_FUNC                      IOMUX_PINCM16_PF_I2C1_SDA
#define GPIO_I2C_0_SCL_PORT                                                GPIOB
#define GPIO_I2C_0_SCL_PIN                                         DL_GPIO_PIN_2
#define GPIO_I2C_0_IOMUX_SCL                                     (IOMUX_PINCM15)
#define GPIO_I2C_0_IOMUX_SCL_FUNC                      IOMUX_PINCM15_PF_I2C1_SCL


/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                           32000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                (115200)
#define UART_0_IBRD_32_MHZ_115200_BAUD                                      (17)
#define UART_0_FBRD_32_MHZ_115200_BAUD                                      (23)
/* Defines for UART_WIT */
#define UART_WIT_INST                                                      UART3
#define UART_WIT_INST_FREQUENCY                                         32000000
#define UART_WIT_INST_IRQHandler                                UART3_IRQHandler
#define UART_WIT_INST_INT_IRQN                                    UART3_INT_IRQn
#define GPIO_UART_WIT_RX_PORT                                              GPIOB
#define GPIO_UART_WIT_RX_PIN                                      DL_GPIO_PIN_13
#define GPIO_UART_WIT_IOMUX_RX                                   (IOMUX_PINCM30)
#define GPIO_UART_WIT_IOMUX_RX_FUNC                    IOMUX_PINCM30_PF_UART3_RX
#define UART_WIT_BAUD_RATE                                              (115200)
#define UART_WIT_IBRD_32_MHZ_115200_BAUD                                    (17)
#define UART_WIT_FBRD_32_MHZ_115200_BAUD                                    (23)
/* Defines for UART_2 */
#define UART_2_INST                                                        UART2
#define UART_2_INST_FREQUENCY                                           32000000
#define UART_2_INST_IRQHandler                                  UART2_IRQHandler
#define UART_2_INST_INT_IRQN                                      UART2_INT_IRQn
#define GPIO_UART_2_RX_PORT                                                GPIOA
#define GPIO_UART_2_TX_PORT                                                GPIOA
#define GPIO_UART_2_RX_PIN                                        DL_GPIO_PIN_22
#define GPIO_UART_2_TX_PIN                                        DL_GPIO_PIN_21
#define GPIO_UART_2_IOMUX_RX                                     (IOMUX_PINCM47)
#define GPIO_UART_2_IOMUX_TX                                     (IOMUX_PINCM46)
#define GPIO_UART_2_IOMUX_RX_FUNC                      IOMUX_PINCM47_PF_UART2_RX
#define GPIO_UART_2_IOMUX_TX_FUNC                      IOMUX_PINCM46_PF_UART2_TX
#define UART_2_BAUD_RATE                                                (115200)
#define UART_2_IBRD_32_MHZ_115200_BAUD                                      (17)
#define UART_2_FBRD_32_MHZ_115200_BAUD                                      (23)




/* Defines for SPI_LCD */
#define SPI_LCD_INST                                                       SPI1
#define SPI_LCD_INST_IRQHandler                                 SPI1_IRQHandler
#define SPI_LCD_INST_INT_IRQN                                     SPI1_INT_IRQn
#define GPIO_SPI_LCD_PICO_PORT                                            GPIOB
#define GPIO_SPI_LCD_PICO_PIN                                     DL_GPIO_PIN_8
#define GPIO_SPI_LCD_IOMUX_PICO                                 (IOMUX_PINCM25)
#define GPIO_SPI_LCD_IOMUX_PICO_FUNC                 IOMUX_PINCM25_PF_SPI1_PICO
/* GPIO configuration for SPI_LCD */
#define GPIO_SPI_LCD_SCLK_PORT                                            GPIOB
#define GPIO_SPI_LCD_SCLK_PIN                                     DL_GPIO_PIN_9
#define GPIO_SPI_LCD_IOMUX_SCLK                                 (IOMUX_PINCM26)
#define GPIO_SPI_LCD_IOMUX_SCLK_FUNC                 IOMUX_PINCM26_PF_SPI1_SCLK



/* Defines for DMA_WIT */
#define DMA_WIT_CHAN_ID                                                      (0)
#define UART_WIT_INST_DMA_TRIGGER                            (DMA_UART3_RX_TRIG)


/* Port definition for Pin Group GPIO_LCD */
#define GPIO_LCD_PORT                                                    (GPIOB)

/* Defines for PIN_RES: GPIOB.10 with pinCMx 27 on package pin 62 */
#define GPIO_LCD_PIN_RES_PIN                                    (DL_GPIO_PIN_10)
#define GPIO_LCD_PIN_RES_IOMUX                                   (IOMUX_PINCM27)
/* Defines for PIN_DC: GPIOB.11 with pinCMx 28 on package pin 63 */
#define GPIO_LCD_PIN_DC_PIN                                     (DL_GPIO_PIN_11)
#define GPIO_LCD_PIN_DC_IOMUX                                    (IOMUX_PINCM28)
/* Defines for PIN_CS: GPIOB.14 with pinCMx 31 on package pin 2 */
#define GPIO_LCD_PIN_CS_PIN                                     (DL_GPIO_PIN_14)
#define GPIO_LCD_PIN_CS_IOMUX                                    (IOMUX_PINCM31)
/* Defines for PIN_BLK: GPIOB.26 with pinCMx 57 on package pin 28 */
#define GPIO_LCD_PIN_BLK_PIN                                    (DL_GPIO_PIN_26)
#define GPIO_LCD_PIN_BLK_IOMUX                                   (IOMUX_PINCM57)
/* Port definition for Pin Group GPIO */
#define GPIO_PORT                                                        (GPIOA)

/* Defines for SCL: GPIOA.1 with pinCMx 2 on package pin 34 */
#define GPIO_SCL_PIN                                             (DL_GPIO_PIN_1)
#define GPIO_SCL_IOMUX                                            (IOMUX_PINCM2)
/* Defines for SDA: GPIOA.0 with pinCMx 1 on package pin 33 */
#define GPIO_SDA_PIN                                             (DL_GPIO_PIN_0)
#define GPIO_SDA_IOMUX                                            (IOMUX_PINCM1)
/* Port definition for Pin Group TB6612 */
#define TB6612_PORT                                                      (GPIOA)

/* Defines for AIN1: GPIOA.14 with pinCMx 36 on package pin 7 */
#define TB6612_AIN1_PIN                                         (DL_GPIO_PIN_14)
#define TB6612_AIN1_IOMUX                                        (IOMUX_PINCM36)
/* Defines for AIN2: GPIOA.15 with pinCMx 37 on package pin 8 */
#define TB6612_AIN2_PIN                                         (DL_GPIO_PIN_15)
#define TB6612_AIN2_IOMUX                                        (IOMUX_PINCM37)
/* Defines for BIN1: GPIOA.12 with pinCMx 34 on package pin 5 */
#define TB6612_BIN1_PIN                                         (DL_GPIO_PIN_12)
#define TB6612_BIN1_IOMUX                                        (IOMUX_PINCM34)
/* Defines for BIN2: GPIOA.13 with pinCMx 35 on package pin 6 */
#define TB6612_BIN2_PIN                                         (DL_GPIO_PIN_13)
#define TB6612_BIN2_IOMUX                                        (IOMUX_PINCM35)
/* Port definition for Pin Group GPIO_ENCODER */
#define GPIO_ENCODER_PORT                                                (GPIOB)

/* Defines for PIN_A: GPIOB.0 with pinCMx 12 on package pin 47 */
// pins affected by this interrupt request:["PIN_A","PIN_B","PIN_C","PIN_D"]
#define GPIO_ENCODER_INT_IRQN                                   (GPIOB_INT_IRQn)
#define GPIO_ENCODER_INT_IIDX                   (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define GPIO_ENCODER_PIN_A_IIDX                              (DL_GPIO_IIDX_DIO0)
#define GPIO_ENCODER_PIN_A_PIN                                   (DL_GPIO_PIN_0)
#define GPIO_ENCODER_PIN_A_IOMUX                                 (IOMUX_PINCM12)
/* Defines for PIN_B: GPIOB.1 with pinCMx 13 on package pin 48 */
#define GPIO_ENCODER_PIN_B_IIDX                              (DL_GPIO_IIDX_DIO1)
#define GPIO_ENCODER_PIN_B_PIN                                   (DL_GPIO_PIN_1)
#define GPIO_ENCODER_PIN_B_IOMUX                                 (IOMUX_PINCM13)
/* Defines for PIN_C: GPIOB.15 with pinCMx 32 on package pin 3 */
#define GPIO_ENCODER_PIN_C_IIDX                             (DL_GPIO_IIDX_DIO15)
#define GPIO_ENCODER_PIN_C_PIN                                  (DL_GPIO_PIN_15)
#define GPIO_ENCODER_PIN_C_IOMUX                                 (IOMUX_PINCM32)
/* Defines for PIN_D: GPIOB.16 with pinCMx 33 on package pin 4 */
#define GPIO_ENCODER_PIN_D_IIDX                             (DL_GPIO_IIDX_DIO16)
#define GPIO_ENCODER_PIN_D_PIN                                  (DL_GPIO_PIN_16)
#define GPIO_ENCODER_PIN_D_IOMUX                                 (IOMUX_PINCM33)
/* Port definition for Pin Group GPIO_XUNJI */
#define GPIO_XUNJI_PORT                                                  (GPIOB)

/* Defines for IO1: GPIOB.6 with pinCMx 23 on package pin 58 */
#define GPIO_XUNJI_IO1_PIN                                       (DL_GPIO_PIN_6)
#define GPIO_XUNJI_IO1_IOMUX                                     (IOMUX_PINCM23)
/* Defines for IO2: GPIOB.7 with pinCMx 24 on package pin 59 */
#define GPIO_XUNJI_IO2_PIN                                       (DL_GPIO_PIN_7)
#define GPIO_XUNJI_IO2_IOMUX                                     (IOMUX_PINCM24)
/* Defines for IO3: GPIOB.17 with pinCMx 43 on package pin 14 */
#define GPIO_XUNJI_IO3_PIN                                      (DL_GPIO_PIN_17)
#define GPIO_XUNJI_IO3_IOMUX                                     (IOMUX_PINCM43)
/* Defines for IO4: GPIOB.18 with pinCMx 44 on package pin 15 */
#define GPIO_XUNJI_IO4_PIN                                      (DL_GPIO_PIN_18)
#define GPIO_XUNJI_IO4_IOMUX                                     (IOMUX_PINCM44)
/* Defines for IO5: GPIOB.19 with pinCMx 45 on package pin 16 */
#define GPIO_XUNJI_IO5_PIN                                      (DL_GPIO_PIN_19)
#define GPIO_XUNJI_IO5_IOMUX                                     (IOMUX_PINCM45)
/* Defines for IO6: GPIOB.20 with pinCMx 48 on package pin 19 */
#define GPIO_XUNJI_IO6_PIN                                      (DL_GPIO_PIN_20)
#define GPIO_XUNJI_IO6_IOMUX                                     (IOMUX_PINCM48)
/* Defines for IO7: GPIOB.21 with pinCMx 49 on package pin 20 */
#define GPIO_XUNJI_IO7_PIN                                      (DL_GPIO_PIN_21)
#define GPIO_XUNJI_IO7_IOMUX                                     (IOMUX_PINCM49)
/* Defines for IO8: GPIOB.22 with pinCMx 50 on package pin 21 */
#define GPIO_XUNJI_IO8_PIN                                      (DL_GPIO_PIN_22)
#define GPIO_XUNJI_IO8_IOMUX                                     (IOMUX_PINCM50)
/* Defines for IO9: GPIOB.23 with pinCMx 51 on package pin 22 */
#define GPIO_XUNJI_IO9_PIN                                      (DL_GPIO_PIN_23)
#define GPIO_XUNJI_IO9_IOMUX                                     (IOMUX_PINCM51)
/* Defines for IO10: GPIOB.24 with pinCMx 52 on package pin 23 */
#define GPIO_XUNJI_IO10_PIN                                     (DL_GPIO_PIN_24)
#define GPIO_XUNJI_IO10_IOMUX                                    (IOMUX_PINCM52)
/* Defines for IO11: GPIOB.25 with pinCMx 56 on package pin 27 */
#define GPIO_XUNJI_IO11_PIN                                     (DL_GPIO_PIN_25)
#define GPIO_XUNJI_IO11_IOMUX                                    (IOMUX_PINCM56)
/* Defines for IO12: GPIOB.27 with pinCMx 58 on package pin 29 */
#define GPIO_XUNJI_IO12_PIN                                     (DL_GPIO_PIN_27)
#define GPIO_XUNJI_IO12_IOMUX                                    (IOMUX_PINCM58)
/* Port definition for Pin Group KEY */
#define KEY_PORT                                                         (GPIOA)

/* Defines for PIN_24: GPIOA.24 with pinCMx 54 on package pin 25 */
#define KEY_PIN_24_PIN                                          (DL_GPIO_PIN_24)
#define KEY_PIN_24_IOMUX                                         (IOMUX_PINCM54)
/* Defines for PIN_25: GPIOA.25 with pinCMx 55 on package pin 26 */
#define KEY_PIN_25_PIN                                          (DL_GPIO_PIN_25)
#define KEY_PIN_25_IOMUX                                         (IOMUX_PINCM55)
/* Defines for PIN_26: GPIOA.26 with pinCMx 59 on package pin 30 */
#define KEY_PIN_26_PIN                                          (DL_GPIO_PIN_26)
#define KEY_PIN_26_IOMUX                                         (IOMUX_PINCM59)
/* Defines for PIN_27: GPIOA.27 with pinCMx 60 on package pin 31 */
#define KEY_PIN_27_PIN                                          (DL_GPIO_PIN_27)
#define KEY_PIN_27_IOMUX                                         (IOMUX_PINCM60)



/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_0_init(void);
void SYSCFG_DL_TIMER_TICK_init(void);
void SYSCFG_DL_I2C_0_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_UART_WIT_init(void);
void SYSCFG_DL_UART_2_init(void);
void SYSCFG_DL_SPI_LCD_init(void);
void SYSCFG_DL_DMA_init(void);

void SYSCFG_DL_SYSTICK_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
