Dependencies for Project 'empty_LP_MSPM0G3507_nortos_keil', Target 'empty_LP_MSPM0G3507_nortos_keil': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (../empty.c)(0x6888623F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/empty.o -MMD)
I (..\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\empty.h)(0x6887B045)
I (..\BSP\hw_lcd.h)(0x6887B045)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\HARDWARE\app_ui.h)(0x6887B045)
I (..\HARDWARE\xunji.h)(0x6887B045)
I (..\..\Temp_MSPM0G3507_Yuntai\empty.h)(0x6887B045)
I (..\BSP\board.h)(0x6887ACE4)
I (..\BSP\Key.h)(0x6887B045)
I (..\BSP\Led.h)(0x6883CDDB)
I (..\HARDWARE\encoder.h)(0x68727A40)
I (..\HARDWARE\PWM.h)(0x686FE66E)
I (..\WIT\wit.h)(0x6872501F)
I (..\HARDWARE\oled.h)(0x6872390F)
I (..\HARDWARE\PID.h)(0x68724EB6)
I (..\HARDWARE\ZDT.h)(0x6887ABC5)
F (..\empty.h)(0x6887B045)()
F (../empty.syscfg)(0x6887AFE5)()
F (startup_mspm0g350x_uvision.s)(0x68870F0E)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-Wa,armasm,--pd,"__UVISION_VERSION SETA 542" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (../ti_msp_dl_config.h)(0x6887AFC1)()
F (../ti_msp_dl_config.c)(0x6887AFEA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MMD)
I (..\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
F (..\HARDWARE\app_ui.c)(0x6887B045)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/app_ui.o -MMD)
I (..\..\Temp_MSPM0G3507_Yuntai\empty.h)(0x6887B045)
I (..\BSP\hw_lcd.h)(0x6887B045)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\HARDWARE\app_ui.h)(0x6887B045)
I (..\HARDWARE\xunji.h)(0x6887B045)
I (..\BSP\board.h)(0x6887ACE4)
I (..\BSP\Key.h)(0x6887B045)
I (..\BSP\Led.h)(0x6883CDDB)
I (..\HARDWARE\encoder.h)(0x68727A40)
I (..\HARDWARE\PWM.h)(0x686FE66E)
I (..\WIT\wit.h)(0x6872501F)
I (..\HARDWARE\oled.h)(0x6872390F)
I (..\HARDWARE\PID.h)(0x68724EB6)
I (..\HARDWARE\ZDT.h)(0x6887ABC5)
F (..\HARDWARE\app_ui.h)(0x6887B045)()
F (..\HARDWARE\codetab.h)(0x686FCC8E)()
F (..\HARDWARE\encoder.c)(0x68727A40)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/encoder.o -MMD)
I (..\HARDWARE\encoder.h)(0x68727A40)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\..\Temp_MSPM0G3507_Yuntai\empty.h)(0x6887B045)
I (..\BSP\hw_lcd.h)(0x6887B045)
I (..\HARDWARE\app_ui.h)(0x6887B045)
I (..\HARDWARE\xunji.h)(0x6887B045)
I (..\BSP\board.h)(0x6887ACE4)
I (..\BSP\Key.h)(0x6887B045)
I (..\BSP\Led.h)(0x6883CDDB)
I (..\HARDWARE\PWM.h)(0x686FE66E)
I (..\WIT\wit.h)(0x6872501F)
I (..\HARDWARE\oled.h)(0x6872390F)
I (..\HARDWARE\PID.h)(0x68724EB6)
I (..\HARDWARE\ZDT.h)(0x6887ABC5)
F (..\HARDWARE\encoder.h)(0x68727A40)()
F (..\HARDWARE\oled.c)(0x6872391A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/oled.o -MMD)
I (..\HARDWARE\oled.h)(0x6872390F)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\BSP\board.h)(0x6887ACE4)
I (..\HARDWARE\codetab.h)(0x686FCC8E)
F (..\HARDWARE\oled.h)(0x6872390F)()
F (..\HARDWARE\oledfont.h)(0x68707112)()
F (..\HARDWARE\PID.c)(0x6872473C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/pid.o -MMD)
I (..\HARDWARE\PID.h)(0x68724EB6)
I (..\..\Temp_MSPM0G3507_Yuntai\empty.h)(0x6887B045)
I (..\BSP\hw_lcd.h)(0x6887B045)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\HARDWARE\app_ui.h)(0x6887B045)
I (..\HARDWARE\xunji.h)(0x6887B045)
I (..\BSP\board.h)(0x6887ACE4)
I (..\BSP\Key.h)(0x6887B045)
I (..\BSP\Led.h)(0x6883CDDB)
I (..\HARDWARE\encoder.h)(0x68727A40)
I (..\HARDWARE\PWM.h)(0x686FE66E)
I (..\WIT\wit.h)(0x6872501F)
I (..\HARDWARE\oled.h)(0x6872390F)
I (..\HARDWARE\ZDT.h)(0x6887ABC5)
F (..\HARDWARE\PID.h)(0x68724EB6)()
F (..\HARDWARE\PWM.c)(0x6871C245)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/pwm.o -MMD)
I (..\HARDWARE\PWM.h)(0x686FE66E)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
F (..\HARDWARE\PWM.h)(0x686FE66E)()
F (..\HARDWARE\xunji.c)(0x68850C43)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/xunji.o -MMD)
I (..\..\Temp_MSPM0G3507_Yuntai\empty.h)(0x6887B045)
I (..\BSP\hw_lcd.h)(0x6887B045)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\HARDWARE\app_ui.h)(0x6887B045)
I (..\HARDWARE\xunji.h)(0x6887B045)
I (..\BSP\board.h)(0x6887ACE4)
I (..\BSP\Key.h)(0x6887B045)
I (..\BSP\Led.h)(0x6883CDDB)
I (..\HARDWARE\encoder.h)(0x68727A40)
I (..\HARDWARE\PWM.h)(0x686FE66E)
I (..\WIT\wit.h)(0x6872501F)
I (..\HARDWARE\oled.h)(0x6872390F)
I (..\HARDWARE\PID.h)(0x68724EB6)
I (..\HARDWARE\ZDT.h)(0x6887ABC5)
F (..\HARDWARE\xunji.h)(0x6887B045)()
F (..\HARDWARE\ZDT.c)(0x68883630)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/zdt.o -MMD)
I (..\HARDWARE\ZDT.h)(0x6887ABC5)
I (..\..\Temp_MSPM0G3507_Yuntai\empty.h)(0x6887B045)
I (..\BSP\hw_lcd.h)(0x6887B045)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\HARDWARE\app_ui.h)(0x6887B045)
I (..\HARDWARE\xunji.h)(0x6887B045)
I (..\BSP\board.h)(0x6887ACE4)
I (..\BSP\Key.h)(0x6887B045)
I (..\BSP\Led.h)(0x6883CDDB)
I (..\HARDWARE\encoder.h)(0x68727A40)
I (..\HARDWARE\PWM.h)(0x686FE66E)
I (..\WIT\wit.h)(0x6872501F)
I (..\HARDWARE\oled.h)(0x6872390F)
I (..\HARDWARE\PID.h)(0x68724EB6)
F (..\HARDWARE\ZDT.h)(0x6887ABC5)()
F (..\BSP\board.c)(0x6887C65F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/board.o -MMD)
I (..\..\Temp_MSPM0G3507_Yuntai\empty.h)(0x6887B045)
I (..\BSP\hw_lcd.h)(0x6887B045)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\HARDWARE\app_ui.h)(0x6887B045)
I (..\HARDWARE\xunji.h)(0x6887B045)
I (..\BSP\board.h)(0x6887ACE4)
I (..\BSP\Key.h)(0x6887B045)
I (..\BSP\Led.h)(0x6883CDDB)
I (..\HARDWARE\encoder.h)(0x68727A40)
I (..\HARDWARE\PWM.h)(0x686FE66E)
I (..\WIT\wit.h)(0x6872501F)
I (..\HARDWARE\oled.h)(0x6872390F)
I (..\HARDWARE\PID.h)(0x68724EB6)
I (..\HARDWARE\ZDT.h)(0x6887ABC5)
F (..\BSP\board.h)(0x6887ACE4)()
F (..\BSP\hw_lcd.c)(0x6887B045)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/hw_lcd.o -MMD)
I (..\BSP\hw_lcd.h)(0x6887B045)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\BSP\board.h)(0x6887ACE4)
I (..\BSP\lcdfont.h)(0x6883C30C)
F (..\BSP\hw_lcd.h)(0x6887B045)()
F (..\BSP\Key.c)(0x6887B045)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/key.o -MMD)
I (..\..\Temp_MSPM0G3507_Yuntai\empty.h)(0x6887B045)
I (..\BSP\hw_lcd.h)(0x6887B045)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\HARDWARE\app_ui.h)(0x6887B045)
I (..\HARDWARE\xunji.h)(0x6887B045)
I (..\BSP\board.h)(0x6887ACE4)
I (..\BSP\Key.h)(0x6887B045)
I (..\BSP\Led.h)(0x6883CDDB)
I (..\HARDWARE\encoder.h)(0x68727A40)
I (..\HARDWARE\PWM.h)(0x686FE66E)
I (..\WIT\wit.h)(0x6872501F)
I (..\HARDWARE\oled.h)(0x6872390F)
I (..\HARDWARE\PID.h)(0x68724EB6)
I (..\HARDWARE\ZDT.h)(0x6887ABC5)
F (..\BSP\Key.h)(0x6887B045)()
F (..\BSP\lcdfont.h)(0x6883C30C)()
F (..\WIT\bsp_mpu6050.c)(0x686FCCD6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bsp_mpu6050.o -MMD)
I (..\WIT\bsp_mpu6050.h)(0x686FCC8E)
I (..\BSP\board.h)(0x6887ACE4)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
F (..\WIT\bsp_mpu6050.h)(0x686FCC8E)()
F (..\WIT\dmpKey.h)(0x686FCC8E)()
F (..\WIT\dmpmap.h)(0x686FCC8E)()
F (..\WIT\inv_mpu.c)(0x686FCC8F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/inv_mpu.o -MMD)
I (..\WIT\inv_mpu.h)(0x686FCC8F)
I (..\BSP\board.h)(0x6887ACE4)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\WIT\inv_mpu_dmp_motion_driver.h)(0x686FCC90)
I (..\WIT\bsp_mpu6050.h)(0x686FCC8E)
F (..\WIT\inv_mpu.h)(0x686FCC8F)()
F (..\WIT\inv_mpu_dmp_motion_driver.c)(0x686FCC90)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/inv_mpu_dmp_motion_driver.o -MMD)
I (..\WIT\inv_mpu.h)(0x686FCC8F)
I (..\BSP\board.h)(0x6887ACE4)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\WIT\inv_mpu_dmp_motion_driver.h)(0x686FCC90)
I (..\WIT\dmpKey.h)(0x686FCC8E)
I (..\WIT\dmpmap.h)(0x686FCC8E)
F (..\WIT\inv_mpu_dmp_motion_driver.h)(0x686FCC90)()
F (..\WIT\wit.c)(0x687261B7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../../Temp_MSPM0G3507_Yuntai -I ../BSP -I ../HARDWARE -I ../WIT

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/wit.o -MMD)
I (..\WIT\wit.h)(0x6872501F)
I (..\..\Temp_MSPM0G3507_Yuntai\ti_msp_dl_config.h)(0x6887AFC1)
I (..\..\source\ti\devices\msp\msp.h)(0x67C10125)
I (..\..\source\ti\devices\DeviceFamily.h)(0x67C10125)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x67C10125)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x67C10125)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\driverlib.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aes.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_comp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_dma.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_opa.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_spi.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timera.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timer.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_trng.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_vref.h)(0x67C10125)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x67C10125)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x67C10125)
I (..\BSP\board.h)(0x6887ACE4)
F (..\WIT\wit.h)(0x6872501F)()
