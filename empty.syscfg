/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.23.1+4034"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const SPI     = scripting.addModule("/ti/driverlib/SPI", {}, false);
const SPI1    = SPI.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();
const UART3   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const pinFunction4        = system.clockTree["HFXT"];
pinFunction4.enable       = true;
pinFunction4.inputFreq    = 40;
pinFunction4.HFCLKMonitor = true;
pinFunction4.HFXTStartup  = 10;

GPIO1.$name                          = "GPIO_LCD";
GPIO1.associatedPins.create(4);
GPIO1.associatedPins[0].$name        = "PIN_RES";
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[0].assignedPin  = "10";
GPIO1.associatedPins[1].$name        = "PIN_DC";
GPIO1.associatedPins[1].assignedPort = "PORTB";
GPIO1.associatedPins[1].assignedPin  = "11";
GPIO1.associatedPins[2].$name        = "PIN_CS";
GPIO1.associatedPins[2].assignedPort = "PORTB";
GPIO1.associatedPins[2].assignedPin  = "14";
GPIO1.associatedPins[3].$name        = "PIN_BLK";
GPIO1.associatedPins[3].assignedPort = "PORTB";
GPIO1.associatedPins[3].assignedPin  = "26";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                         = "GPIO";
GPIO2.port                          = "PORTA";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].$name       = "SCL";
GPIO2.associatedPins[0].ioStructure = "OD";
GPIO2.associatedPins[0].assignedPin = "1";
GPIO2.associatedPins[1].$name       = "SDA";
GPIO2.associatedPins[1].ioStructure = "OD";
GPIO2.associatedPins[1].assignedPin = "0";

GPIO3.$name                          = "TB6612";
GPIO3.port                           = "PORTA";
GPIO3.associatedPins.create(4);
GPIO3.associatedPins[0].$name        = "AIN1";
GPIO3.associatedPins[0].initialValue = "SET";
GPIO3.associatedPins[0].assignedPin  = "14";
GPIO3.associatedPins[1].$name        = "AIN2";
GPIO3.associatedPins[1].initialValue = "SET";
GPIO3.associatedPins[1].assignedPin  = "15";
GPIO3.associatedPins[2].$name        = "BIN1";
GPIO3.associatedPins[2].initialValue = "SET";
GPIO3.associatedPins[2].assignedPin  = "12";
GPIO3.associatedPins[3].$name        = "BIN2";
GPIO3.associatedPins[3].initialValue = "SET";
GPIO3.associatedPins[3].assignedPin  = "13";

GPIO4.$name                               = "GPIO_ENCODER";
GPIO4.associatedPins.create(4);
GPIO4.associatedPins[0].$name             = "PIN_A";
GPIO4.associatedPins[0].direction         = "INPUT";
GPIO4.associatedPins[0].assignedPort      = "PORTB";
GPIO4.associatedPins[0].assignedPin       = "0";
GPIO4.associatedPins[0].interruptEn       = true;
GPIO4.associatedPins[0].interruptPriority = "0";
GPIO4.associatedPins[0].polarity          = "RISE";
GPIO4.associatedPins[1].$name             = "PIN_B";
GPIO4.associatedPins[1].direction         = "INPUT";
GPIO4.associatedPins[1].assignedPort      = "PORTB";
GPIO4.associatedPins[1].assignedPin       = "1";
GPIO4.associatedPins[1].interruptEn       = true;
GPIO4.associatedPins[1].interruptPriority = "0";
GPIO4.associatedPins[1].polarity          = "RISE";
GPIO4.associatedPins[2].$name             = "PIN_C";
GPIO4.associatedPins[2].direction         = "INPUT";
GPIO4.associatedPins[2].assignedPort      = "PORTB";
GPIO4.associatedPins[2].assignedPin       = "15";
GPIO4.associatedPins[2].interruptEn       = true;
GPIO4.associatedPins[2].interruptPriority = "0";
GPIO4.associatedPins[2].polarity          = "RISE";
GPIO4.associatedPins[3].$name             = "PIN_D";
GPIO4.associatedPins[3].direction         = "INPUT";
GPIO4.associatedPins[3].assignedPort      = "PORTB";
GPIO4.associatedPins[3].assignedPin       = "16";
GPIO4.associatedPins[3].interruptEn       = true;
GPIO4.associatedPins[3].interruptPriority = "0";
GPIO4.associatedPins[3].polarity          = "RISE";

GPIO5.$name                          = "GPIO_XUNJI";
GPIO5.port                           = "PORTB";
GPIO5.associatedPins.create(12);
GPIO5.associatedPins[0].$name        = "IO1";
GPIO5.associatedPins[0].direction    = "INPUT";
GPIO5.associatedPins[0].assignedPin  = "6";
GPIO5.associatedPins[0].pin.$assign  = "PB6";
GPIO5.associatedPins[1].$name        = "IO2";
GPIO5.associatedPins[1].direction    = "INPUT";
GPIO5.associatedPins[1].assignedPin  = "7";
GPIO5.associatedPins[1].pin.$assign  = "PB7";
GPIO5.associatedPins[2].$name        = "IO3";
GPIO5.associatedPins[2].direction    = "INPUT";
GPIO5.associatedPins[2].assignedPin  = "17";
GPIO5.associatedPins[3].$name        = "IO4";
GPIO5.associatedPins[3].direction    = "INPUT";
GPIO5.associatedPins[3].assignedPin  = "18";
GPIO5.associatedPins[4].$name        = "IO5";
GPIO5.associatedPins[4].direction    = "INPUT";
GPIO5.associatedPins[4].assignedPin  = "19";
GPIO5.associatedPins[4].pin.$assign  = "PB19";
GPIO5.associatedPins[5].$name        = "IO6";
GPIO5.associatedPins[5].direction    = "INPUT";
GPIO5.associatedPins[5].assignedPin  = "20";
GPIO5.associatedPins[5].pin.$assign  = "PB20";
GPIO5.associatedPins[6].$name        = "IO7";
GPIO5.associatedPins[6].direction    = "INPUT";
GPIO5.associatedPins[6].assignedPin  = "21";
GPIO5.associatedPins[6].pin.$assign  = "PB21";
GPIO5.associatedPins[7].$name        = "IO8";
GPIO5.associatedPins[7].direction    = "INPUT";
GPIO5.associatedPins[7].assignedPin  = "22";
GPIO5.associatedPins[7].pin.$assign  = "PB22";
GPIO5.associatedPins[8].$name        = "IO9";
GPIO5.associatedPins[8].direction    = "INPUT";
GPIO5.associatedPins[8].assignedPin  = "23";
GPIO5.associatedPins[8].pin.$assign  = "PB23";
GPIO5.associatedPins[9].$name        = "IO10";
GPIO5.associatedPins[9].direction    = "INPUT";
GPIO5.associatedPins[9].assignedPin  = "24";
GPIO5.associatedPins[10].$name       = "IO11";
GPIO5.associatedPins[10].direction   = "INPUT";
GPIO5.associatedPins[10].assignedPin = "25";
GPIO5.associatedPins[11].$name       = "IO12";
GPIO5.associatedPins[11].assignedPin = "27";

GPIO6.$name                              = "KEY";
GPIO6.port                               = "PORTA";
GPIO6.associatedPins.create(4);
GPIO6.associatedPins[0].direction        = "INPUT";
GPIO6.associatedPins[0].$name            = "PIN_24";
GPIO6.associatedPins[0].internalResistor = "PULL_UP";
GPIO6.associatedPins[0].assignedPin      = "24";
GPIO6.associatedPins[1].$name            = "PIN_25";
GPIO6.associatedPins[1].direction        = "INPUT";
GPIO6.associatedPins[1].assignedPin      = "25";
GPIO6.associatedPins[1].internalResistor = "PULL_UP";
GPIO6.associatedPins[2].$name            = "PIN_26";
GPIO6.associatedPins[2].direction        = "INPUT";
GPIO6.associatedPins[2].assignedPin      = "26";
GPIO6.associatedPins[2].internalResistor = "PULL_UP";
GPIO6.associatedPins[3].direction        = "INPUT";
GPIO6.associatedPins[3].internalResistor = "PULL_UP";
GPIO6.associatedPins[3].$name            = "PIN_27";
GPIO6.associatedPins[3].assignedPin      = "27";

// 添加步进电机GPIO配置
const GPIO7 = GPIO.addInstance();
GPIO7.$name                          = "STEPPER_MOTOR";
GPIO7.port                           = "PORTA";
GPIO7.associatedPins.create(3);
GPIO7.associatedPins[0].$name        = "STEP";
GPIO7.associatedPins[0].assignedPin  = "18";
GPIO7.associatedPins[0].initialValue = "CLEAR";
GPIO7.associatedPins[1].$name        = "DIR";
GPIO7.associatedPins[1].assignedPin  = "23";
GPIO7.associatedPins[1].initialValue = "CLEAR";
GPIO7.associatedPins[2].$name        = "EN";
GPIO7.associatedPins[2].assignedPin  = "28";
GPIO7.associatedPins[2].initialValue = "SET";

I2C1.$name                     = "I2C_0";
I2C1.basicEnableController     = true;
I2C1.basicControllerBusSpeed   = 400000;
I2C1.peripheral.sdaPin.$assign = "PB3";
I2C1.peripheral.sclPin.$assign = "PB2";
I2C1.sdaPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric4";
I2C1.sclPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric5";

PWM1.$name                      = "PWM_0";
PWM1.clockDivider               = 8;
PWM1.timerStartTimer            = true;
PWM1.peripheral.$assign         = "TIMA1";
PWM1.peripheral.ccp0Pin.$assign = "PA17";
PWM1.peripheral.ccp1Pin.$assign = "PA16";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.invert       = true;
PWM1.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.invert       = true;
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric6";
PWM1.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric8";

SPI1.$name                              = "SPI_LCD";
SPI1.frameFormat                        = "MOTO3";
SPI1.direction                          = "PICO";
SPI1.targetBitRate                      = 16000000;
SPI1.sclkPinConfig.direction            = scripting.forceWrite("OUTPUT");
SPI1.sclkPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.sclkPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.sclkPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
SPI1.mosiPinConfig.direction            = scripting.forceWrite("OUTPUT");
SPI1.mosiPinConfig.hideOutputInversion  = scripting.forceWrite(false);
SPI1.mosiPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.mosiPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.mosiPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
SPI1.peripheral.$assign                 = "SPI1";
SPI1.peripheral.sclkPin.$assign         = "PB9";
SPI1.peripheral.mosiPin.$assign         = "PB8";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable  = true;
SYSTICK.systickEnable = true;
SYSTICK.period        = 32;

TIMER1.$name              = "TIMER_TICK";
TIMER1.timerClkDiv        = 8;
TIMER1.timerClkPrescale   = 10;
TIMER1.timerPeriod        = "1 ms";
TIMER1.timerMode          = "PERIODIC";
TIMER1.interrupts         = ["ZERO"];
TIMER1.interruptPriority  = "3";
TIMER1.peripheral.$assign = "TIMG0";

UART1.$name                    = "UART_0";
UART1.enabledInterrupts        = ["RX"];
UART1.interruptPriority        = "3";
UART1.targetBaudRate           = 115200;
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

UART2.$name                            = "UART_WIT";
UART2.targetBaudRate                   = 115200;
UART2.enableFIFO                       = true;
UART2.rxTimeoutValue                   = 1;
UART2.enabledInterrupts                = ["RX_TIMEOUT_ERROR"];
UART2.enabledDMARXTriggers             = "DL_UART_DMA_INTERRUPT_RX";
UART2.direction                        = "RX";
UART2.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";
UART2.DMA_CHANNEL_RX.$name             = "DMA_WIT";
UART2.DMA_CHANNEL_RX.srcLength         = "BYTE";
UART2.DMA_CHANNEL_RX.dstLength         = "BYTE";
UART2.DMA_CHANNEL_RX.addressMode       = "f2b";
UART2.peripheral.$assign               = "UART3";
UART2.peripheral.rxPin.$assign         = "PB13";

UART3.$name                    = "UART_2";
UART3.targetBaudRate           = 115200;
UART3.enabledInterrupts        = ["RX"];
UART3.peripheral.$assign       = "UART2";
UART3.peripheral.rxPin.$assign = "PA22";
UART3.peripheral.txPin.$assign = "PA21";
UART3.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric9";
UART3.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric10";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
GPIO1.associatedPins[0].pin.$suggestSolution       = "PB10";
GPIO1.associatedPins[1].pin.$suggestSolution       = "PB11";
GPIO1.associatedPins[2].pin.$suggestSolution       = "PB14";
GPIO1.associatedPins[3].pin.$suggestSolution       = "PB26";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution       = "PA1";
GPIO2.associatedPins[1].pin.$suggestSolution       = "PA0";
GPIO3.associatedPins[0].pin.$suggestSolution       = "PA14";
GPIO3.associatedPins[1].pin.$suggestSolution       = "PA15";
GPIO3.associatedPins[2].pin.$suggestSolution       = "PA12";
GPIO3.associatedPins[3].pin.$suggestSolution       = "PA13";
GPIO4.associatedPins[0].pin.$suggestSolution       = "PB0";
GPIO4.associatedPins[1].pin.$suggestSolution       = "PB1";
GPIO4.associatedPins[2].pin.$suggestSolution       = "PB15";
GPIO4.associatedPins[3].pin.$suggestSolution       = "PB16";
GPIO5.associatedPins[2].pin.$suggestSolution       = "PB17";
GPIO5.associatedPins[3].pin.$suggestSolution       = "PB18";
GPIO5.associatedPins[9].pin.$suggestSolution       = "PB24";
GPIO5.associatedPins[10].pin.$suggestSolution      = "PB25";
GPIO5.associatedPins[11].pin.$suggestSolution      = "PB27";
GPIO6.associatedPins[0].pin.$suggestSolution       = "PA24";
GPIO6.associatedPins[1].pin.$suggestSolution       = "PA25";
GPIO6.associatedPins[2].pin.$suggestSolution       = "PA26";
GPIO6.associatedPins[3].pin.$suggestSolution       = "PA27";
I2C1.peripheral.$suggestSolution                   = "I2C1";
UART1.peripheral.$suggestSolution                  = "UART0";
UART2.DMA_CHANNEL_RX.peripheral.$suggestSolution   = "DMA_CH0";
