#include "ti_msp_dl_config.h"

#include "empty.h"
#include "HARDWARE/stepper_motor.h"

// ����������ֱ�洢5��������״̬
int key24_val = 0; // ��ӦKEY_24��0-1ѭ��������ҳ���л���
int key25_val = 0;
int key26_val = 0;
int key27_val = 0; // ��ӦKEY_27��0-2ѭ��������ҳ���л���

// ��һ�εİ���״̬
int last_key24_val = 0;
int last_key25_val = 0;
int last_key26_val = 0;
int last_key27_val = 0;

// ҳ������
int Ui_Index = 0;
int Ui_Disp = 0;
const int MAX_UI_INDEX = 1;
void Read_Key(void); // ������ȡ��������
void UiDispatcher(void); // ҳ����Ⱥ�������
void handle_ui_home_one(void); // ҳ�洦����������
void handle_ui_left_right_page(void);  // ҳ�洦����������
void handle_ui_question_page(void); // ҳ�洦����������

int yaw;

int main(void)
{
    board_init();
//    lcd_init();
//    ui_home_page();
//		WIT_Init();
//    OLED_Init();
		Motor_Init();

//		OLED_Clear();
//    star_expansion_fullscreen_animation();
//    ui_home_One();

		// Emm_V5.0步进电机驱动器初始化和恒速转动示例
		EMM_V5_Init();                  // 初始化Emm_V5.0驱动器
		delay_ms(100);

		EMM_V5_Enable(1);               // 使能步进电机
		delay_ms(50);

		// 设置步进电机以60RPM恒速顺时针转动
		EMM_V5_Start_Continuous_Rotation(EMM_DIR_CW, 60);  // 开始60RPM恒速转动

		while(1)
		{
					// 步进电机将以设定的速度持续转动
					// 可以在这里添加其他控制逻辑

					delay_ms(100);  // 主循环延时

					// 示例：每10秒改变一次方向
					static uint32_t direction_counter = 0;
					direction_counter++;
					if (direction_counter >= 100) {  // 10秒 (100 * 100ms)
						direction_counter = 0;

						// 停止电机
						EMM_V5_Stop_Immediately();
						delay_ms(500);

						// 改变方向并重新开始转动
						static uint8_t dir_flag = 0;
						dir_flag = !dir_flag;
						if (dir_flag) {
							EMM_V5_Start_Continuous_Rotation(EMM_DIR_CCW, 60);  // 逆时针60RPM
						} else {
							EMM_V5_Start_Continuous_Rotation(EMM_DIR_CW, 60);   // 顺时针60RPM
						}
					}
		}
	
//    while (1)
//    {
//        Read_Key();
//			
//				yaw = (int) wit_data.yaw;
//				OLED_Float(12, 50, wit_data.yaw, 1);
//				OLED_Float(14, 50, yaw, 1);
//			
//        UiDispatcher();
//			
//				xunji();
//			
//        delay_ms(10);
//    }
}

// ������ȡ�������ɼ�������
void Read_Key(void)
{
    key24_val = Key_Read(KEY_24);
    key25_val = Key_Read(KEY_25);
    key26_val = Key_Read(KEY_26);
    key27_val = Key_Read(KEY_27);
}

// ҳ����Ⱥ�������ѭ��ֻ���������������������������
void UiDispatcher(void)
{
    // ����Ƿ�������һ������������
    if (!(my_GPIO_readPin(KEY_PORT, KEY_PIN_24_PIN) &&
          my_GPIO_readPin(KEY_PORT, KEY_PIN_25_PIN) &&
          my_GPIO_readPin(KEY_PORT, KEY_PIN_26_PIN) &&
          my_GPIO_readPin(KEY_PORT, KEY_PIN_27_PIN)))
    {
        // KEY_24��ҳ���л�
        if (key24_val != last_key24_val)
        {
            last_key24_val = key24_val;
            LCD_Fill(0, 0, LCD_W, LCD_H, PURE_BLACK); // ��Ļ���
            Ui_Index++;
            if (Ui_Index > MAX_UI_INDEX) Ui_Index = 0;
            Ui_Disp = 0;
        }

        // KEY_27����ҳ���л�
        if (key27_val != last_key27_val)
        {
            last_key27_val = key27_val;
            // ����չҳ���л��߼�
        }

        // ҳ����ʾ�߼�
        switch (Ui_Index)
        {
            case 0:
                handle_ui_home_one();
                break;
            case 1:
                if (key27_val == 0)
                    handle_ui_left_right_page();
                else if (key27_val == 1)
                    handle_ui_question_page();
                else
                    handle_ui_left_right_page();
                break;
            default:
                Ui_Index = 0;
                handle_ui_home_one();
                break;
        }
    }
}

// ҳ�洦������ʵ��
void handle_ui_home_one(void)
{
    ui_home_One();
    // ��ҳ�°�����Ӧ���ڴ���չ
}

void handle_ui_left_right_page(void)
{
    ui_left_right_page();
    // ʾ�����������KEY_25����ִ��ĳ����
//    if (key25_val != last_key25_val)
//    {

//    }
    // ����������Ӧ��
}

void handle_ui_question_page(void)
{
    ui_question_page();
    // ʾ�����������KEY_26����ִ��ĳ����
//    if (key26_val != last_key26_val)
//    {

//    }
    // ����������Ӧ��
}

// Emm_V5.0使用UART通信控制，不需要定时器中断处理步进脉冲
