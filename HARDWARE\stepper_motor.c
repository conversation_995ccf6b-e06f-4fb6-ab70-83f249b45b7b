#include "stepper_motor.h"

// 全局变量定义
emm_v5_motor_t emm_motor = {
    .device_addr = EMM_V5_DEVICE_ADDR,
    .current_speed = 0,
    .direction = EMM_DIR_CW,
    .state = EMM_STATE_IDLE,
    .enabled = 0,
    .acceleration = 10  // 默认加速度档位
};

/**
 * @brief Emm_V5.0步进电机驱动器初始化
 */
void EMM_V5_Init(void)
{
    // UART2已在系统配置中初始化

    // 初始化结构体
    emm_motor.state = EMM_STATE_IDLE;
    emm_motor.enabled = 0;
    emm_motor.current_speed = 0;

    // 等待驱动器启动
    delay_ms(100);
}

/**
 * @brief UART发送数据
 * @param data: 发送数据缓冲区
 * @param len: 数据长度
 */
void EMM_V5_UART_Send(uint8_t *data, uint8_t len)
{
    for (uint8_t i = 0; i < len; i++) {
        // 等待发送缓冲区空闲
        while (!DL_UART_isTXFIFOEmpty(EMM_V5_UART_INST));

        // 发送数据
        DL_UART_transmitData(EMM_V5_UART_INST, data[i]);
    }

    // 等待发送完成
    while (DL_UART_isBusy(EMM_V5_UART_INST));
}

/**
 * @brief UART接收数据
 * @param data: 接收数据缓冲区
 * @param max_len: 最大接收长度
 * @param timeout_ms: 超时时间(毫秒)
 * @return 实际接收的字节数
 */
uint8_t EMM_V5_UART_Receive(uint8_t *data, uint8_t max_len, uint32_t timeout_ms)
{
    uint8_t received = 0;
    uint32_t start_time = get_tick_count(); // 需要实现获取系统时间的函数

    while (received < max_len && (get_tick_count() - start_time) < timeout_ms) {
        if (!DL_UART_isRXFIFOEmpty(EMM_V5_UART_INST)) {
            data[received] = DL_UART_receiveData(EMM_V5_UART_INST);
            received++;
        }
    }

    return received;
}

/**
 * @brief 发送Emm_V5.0命令
 * @param cmd: 命令数据
 * @param len: 命令长度
 * @return 1-成功, 0-失败
 */
uint8_t EMM_V5_Send_Command(uint8_t *cmd, uint8_t len)
{
    uint8_t response[4];

    // 发送命令
    EMM_V5_UART_Send(cmd, len);

    // 等待响应
    uint8_t recv_len = EMM_V5_UART_Receive(response, 4, 100);

    // 检查响应
    if (recv_len >= 3 && response[0] == emm_motor.device_addr && response[2] == 0x02) {
        return 1; // 成功
    }

    return 0; // 失败
}

/**
 * @brief 电机使能控制
 * @param enable: 1-使能, 0-禁用
 */
void EMM_V5_Enable(uint8_t enable)
{
    uint8_t cmd[6] = {
        emm_motor.device_addr,  // 地址
        EMM_CMD_ENABLE,         // 功能码
        0xAB,                   // 固定数据
        enable ? 1 : 0,         // 使能状态
        0x00,                   // 多机同步标志
        EMM_V5_CHECKSUM         // 校验字节
    };

    if (EMM_V5_Send_Command(cmd, 6)) {
        emm_motor.enabled = enable;
        if (!enable) {
            emm_motor.state = EMM_STATE_IDLE;
        }
    }
}

/**
 * @brief 速度模式控制
 * @param dir: 转动方向
 * @param rpm: 转速(RPM)
 * @param acceleration: 加速度档位(0-255)
 */
void EMM_V5_Set_Speed_Mode(emm_direction_t dir, uint16_t rpm, uint8_t acceleration)
{
    uint8_t cmd[8] = {
        emm_motor.device_addr,  // 地址
        EMM_CMD_SPEED,          // 功能码 0xF6
        dir,                    // 方向 (0=CW, 1=CCW)
        (uint8_t)(rpm >> 8),    // 速度高字节
        (uint8_t)(rpm & 0xFF),  // 速度低字节
        acceleration,           // 加速度档位
        0x00,                   // 多机同步标志
        EMM_V5_CHECKSUM         // 校验字节
    };

    if (EMM_V5_Send_Command(cmd, 8)) {
        emm_motor.direction = dir;
        emm_motor.current_speed = rpm;
        emm_motor.acceleration = acceleration;
        emm_motor.state = EMM_STATE_RUNNING;
    }
}

/**
 * @brief 位置模式控制
 * @param dir: 转动方向
 * @param rpm: 转速(RPM)
 * @param acceleration: 加速度档位(0-255)
 * @param pulses: 脉冲数
 */
void EMM_V5_Set_Position_Mode(emm_direction_t dir, uint16_t rpm, uint8_t acceleration, uint32_t pulses)
{
    uint8_t cmd[13] = {
        emm_motor.device_addr,      // 地址
        EMM_CMD_POSITION,           // 功能码 0xFD
        dir,                        // 方向
        (uint8_t)(rpm >> 8),        // 速度高字节
        (uint8_t)(rpm & 0xFF),      // 速度低字节
        acceleration,               // 加速度档位
        (uint8_t)(pulses >> 24),    // 脉冲数最高字节
        (uint8_t)(pulses >> 16),    // 脉冲数次高字节
        (uint8_t)(pulses >> 8),     // 脉冲数次低字节
        (uint8_t)(pulses & 0xFF),   // 脉冲数最低字节
        0x00,                       // 相对/绝对位置模式 (0=相对)
        0x00,                       // 多机同步标志
        EMM_V5_CHECKSUM             // 校验字节
    };

    if (EMM_V5_Send_Command(cmd, 13)) {
        emm_motor.direction = dir;
        emm_motor.current_speed = rpm;
        emm_motor.acceleration = acceleration;
        emm_motor.state = EMM_STATE_RUNNING;
    }
}

/**
 * @brief 立即停止电机
 */
void EMM_V5_Stop_Immediately(void)
{
    uint8_t cmd[5] = {
        emm_motor.device_addr,  // 地址
        EMM_CMD_STOP,           // 功能码 0xFE
        0x98,                   // 固定数据
        0x00,                   // 多机同步标志
        EMM_V5_CHECKSUM         // 校验字节
    };

    if (EMM_V5_Send_Command(cmd, 5)) {
        emm_motor.state = EMM_STATE_STOPPED;
        emm_motor.current_speed = 0;
    }
}

/**
 * @brief 开始恒速连续转动 (核心功能)
 * @param dir: 转动方向
 * @param rpm: 转速(RPM)
 */
void EMM_V5_Start_Continuous_Rotation(emm_direction_t dir, uint16_t rpm)
{
    // 首先确保电机已使能
    if (!emm_motor.enabled) {
        EMM_V5_Enable(1);
        delay_ms(50);
    }

    // 设置速度模式进行恒速转动
    // 加速度设置为0表示不使用曲线加减速，直接按设定速度运行
    EMM_V5_Set_Speed_Mode(dir, rpm, 0);
}

/**
 * @brief 获取系统时间计数 (需要根据实际系统实现)
 * @return 当前时间计数(毫秒)
 */
uint32_t get_tick_count(void)
{
    // 这里需要根据实际系统实现
    // 可以使用系统滴答定时器或其他时间基准
    static uint32_t tick_count = 0;
    return tick_count++; // 简化实现，实际应该返回真实的时间
}

// Emm_V5.0驱动器专用代码 - 旧的GPIO控制代码已移除
// 现在使用UART串口通信控制，功能更强大且更稳定
