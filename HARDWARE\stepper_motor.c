#include "stepper_motor.h"

// 微秒延时函数
void delay_us(uint32_t us)
{
    // 基于32MHz系统时钟的简单延时
    // 每个循环大约1微秒 (需要根据实际情况调整)
    volatile uint32_t count = us * 8;  // 32MHz下大约8个时钟周期为1微秒
    while(count--);
}

// 全局变量定义
stepper_motor_t stepper_motor = {
    .step_delay_us = DEFAULT_STEP_DELAY_US,
    .direction = STEPPER_DIR_CW,
    .state = STEPPER_IDLE,
    .target_steps = 0,
    .current_steps = 0,
    .enabled = 0
};

volatile uint8_t step_flag = 0;

/**
 * @brief 步进电机初始化
 */
void Stepper_Motor_Init(void)
{
    // GPIO初始化在ti_msp_dl_config.c中已完成
    
    // 设置初始状态
    DL_GPIO_clearPins(STEPPER_STEP_PORT, STEPPER_STEP_PIN);  // 步进信号低电平
    DL_GPIO_clearPins(STEPPER_DIR_PORT, STEPPER_DIR_PIN);    // 方向信号低电平
    DL_GPIO_setPins(STEPPER_EN_PORT, STEPPER_EN_PIN);        // 使能信号高电平(禁用)
    
    // 初始化结构体
    stepper_motor.state = STEPPER_IDLE;
    stepper_motor.enabled = 0;
    
    // 配置定时器用于步进控制 (使用现有的TIMER_TICK)
    // 定时器已在系统中配置为1ms中断
}

/**
 * @brief 步进电机使能控制
 * @param enable: 1-使能, 0-禁用
 */
void Stepper_Motor_Enable(uint8_t enable)
{
    if (enable) {
        DL_GPIO_clearPins(STEPPER_EN_PORT, STEPPER_EN_PIN);  // 低电平使能
        stepper_motor.enabled = 1;
    } else {
        DL_GPIO_setPins(STEPPER_EN_PORT, STEPPER_EN_PIN);    // 高电平禁用
        stepper_motor.enabled = 0;
        stepper_motor.state = STEPPER_IDLE;
    }
}

/**
 * @brief 设置步进电机方向
 * @param dir: 转动方向
 */
void Stepper_Motor_Set_Direction(stepper_direction_t dir)
{
    stepper_motor.direction = dir;
    
    if (dir == STEPPER_DIR_CW) {
        DL_GPIO_clearPins(STEPPER_DIR_PORT, STEPPER_DIR_PIN);  // 顺时针
    } else {
        DL_GPIO_setPins(STEPPER_DIR_PORT, STEPPER_DIR_PIN);    // 逆时针
    }
}

/**
 * @brief 根据RPM设置步进电机速度
 * @param rpm: 转速 (转/分钟)
 */
void Stepper_Motor_Set_Speed_RPM(float rpm)
{
    uint32_t delay_us = Stepper_RPM_To_Delay(rpm);
    Stepper_Motor_Set_Speed_Delay(delay_us);
}

/**
 * @brief 直接设置步进间隔时间
 * @param delay_us: 步进间隔时间(微秒)
 */
void Stepper_Motor_Set_Speed_Delay(uint32_t delay_us)
{
    // 限制延时范围
    if (delay_us < MIN_STEP_DELAY_US) {
        delay_us = MIN_STEP_DELAY_US;
    } else if (delay_us > MAX_STEP_DELAY_US) {
        delay_us = MAX_STEP_DELAY_US;
    }
    
    stepper_motor.step_delay_us = delay_us;
}

/**
 * @brief 开始连续转动
 */
void Stepper_Motor_Start_Continuous(void)
{
    if (!stepper_motor.enabled) {
        return;
    }
    
    stepper_motor.state = STEPPER_RUNNING;
    stepper_motor.target_steps = 0xFFFFFFFF;  // 连续转动
    stepper_motor.current_steps = 0;
    
    // 启动定时器中断进行步进控制
    step_flag = 1;
}

/**
 * @brief 停止步进电机
 */
void Stepper_Motor_Stop(void)
{
    stepper_motor.state = STEPPER_STOPPED;
    step_flag = 0;
    DL_GPIO_clearPins(STEPPER_STEP_PORT, STEPPER_STEP_PIN);
}

/**
 * @brief 单步进
 */
void Stepper_Motor_Step_Single(void)
{
    if (!stepper_motor.enabled) {
        return;
    }
    
    // 产生步进脉冲
    DL_GPIO_setPins(STEPPER_STEP_PORT, STEPPER_STEP_PIN);
    delay_us(2);  // 脉冲宽度
    DL_GPIO_clearPins(STEPPER_STEP_PORT, STEPPER_STEP_PIN);
    
    stepper_motor.current_steps++;
}

/**
 * @brief 移动指定步数
 * @param steps: 步数
 * @param dir: 方向
 */
void Stepper_Motor_Move_Steps(uint32_t steps, stepper_direction_t dir)
{
    if (!stepper_motor.enabled) {
        return;
    }
    
    Stepper_Motor_Set_Direction(dir);
    stepper_motor.target_steps = steps;
    stepper_motor.current_steps = 0;
    stepper_motor.state = STEPPER_RUNNING;
    
    step_flag = 1;
}

/**
 * @brief 转动指定角度
 * @param angle: 角度(度)
 * @param dir: 方向
 */
void Stepper_Motor_Move_Angle(float angle, stepper_direction_t dir)
{
    uint32_t steps = (uint32_t)((angle / 360.0f) * TOTAL_STEPS_PER_REV);
    Stepper_Motor_Move_Steps(steps, dir);
}

/**
 * @brief RPM转换为延时时间
 * @param rpm: 转速
 * @return 延时时间(微秒)
 */
uint32_t Stepper_RPM_To_Delay(float rpm)
{
    if (rpm <= 0) {
        return MAX_STEP_DELAY_US;
    }
    
    // 计算每步的时间间隔
    // 每分钟rpm转 = 每秒rpm/60转 = 每秒(rpm/60)*TOTAL_STEPS_PER_REV步
    // 每步时间 = 1 / ((rpm/60)*TOTAL_STEPS_PER_REV) 秒
    // 转换为微秒: * 1000000
    
    float steps_per_second = (rpm / 60.0f) * TOTAL_STEPS_PER_REV;
    uint32_t delay_us = (uint32_t)(1000000.0f / steps_per_second);
    
    return delay_us;
}

/**
 * @brief 延时时间转换为RPM
 * @param delay_us: 延时时间(微秒)
 * @return RPM
 */
float Stepper_Delay_To_RPM(uint32_t delay_us)
{
    if (delay_us == 0) {
        return 0;
    }
    
    float steps_per_second = 1000000.0f / delay_us;
    float rpm = (steps_per_second * 60.0f) / TOTAL_STEPS_PER_REV;
    
    return rpm;
}

/**
 * @brief 定时器中断处理函数 (在现有的TIMER_TICK中断中调用)
 */
void Stepper_Timer_IRQHandler(void)
{
    static uint32_t step_counter = 0;
    
    if (!step_flag || stepper_motor.state != STEPPER_RUNNING) {
        return;
    }
    
    // 计算需要的定时器计数 (1ms定时器)
    uint32_t timer_counts = stepper_motor.step_delay_us / 1000;
    if (timer_counts == 0) timer_counts = 1;
    
    step_counter++;
    
    if (step_counter >= timer_counts) {
        step_counter = 0;
        
        // 执行单步
        Stepper_Motor_Step_Single();
        
        // 检查是否完成目标步数
        if (stepper_motor.target_steps != 0xFFFFFFFF && 
            stepper_motor.current_steps >= stepper_motor.target_steps) {
            Stepper_Motor_Stop();
        }
    }
}
