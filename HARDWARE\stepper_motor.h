#ifndef __STEPPER_MOTOR_H__
#define __STEPPER_MOTOR_H__

#include "ti_msp_dl_config.h"
#include "empty.h"

// 步进电机控制引脚定义 (基于天猛星开发板)
#define STEPPER_STEP_PORT    GPIOA
#define STEPPER_STEP_PIN     DL_GPIO_PIN_18    // PA18 - 步进脉冲
#define STEPPER_DIR_PORT     GPIOA  
#define STEPPER_DIR_PIN      DL_GPIO_PIN_23    // PA23 - 方向控制
#define STEPPER_EN_PORT      GPIOA
#define STEPPER_EN_PIN       DL_GPIO_PIN_28    // PA28 - 使能控制

// 步进电机参数定义
#define STEPS_PER_REV        200               // 每圈步数 (1.8度/步)
#define MICROSTEPS           16                // 细分数
#define TOTAL_STEPS_PER_REV  (STEPS_PER_REV * MICROSTEPS)  // 总细分步数

// 速度相关定义
#define MIN_STEP_DELAY_US    500               // 最小步进间隔 (微秒)
#define MAX_STEP_DELAY_US    50000             // 最大步进间隔 (微秒)
#define DEFAULT_STEP_DELAY_US 2000             // 默认步进间隔 (微秒)

// 方向定义
typedef enum {
    STEPPER_DIR_CW  = 0,    // 顺时针
    STEPPER_DIR_CCW = 1     // 逆时针
} stepper_direction_t;

// 步进电机状态
typedef enum {
    STEPPER_IDLE = 0,       // 空闲
    STEPPER_RUNNING,        // 运行中
    STEPPER_STOPPED         // 停止
} stepper_state_t;

// 步进电机控制结构体
typedef struct {
    uint32_t step_delay_us;         // 步进间隔时间(微秒)
    stepper_direction_t direction;  // 转动方向
    stepper_state_t state;          // 当前状态
    uint32_t target_steps;          // 目标步数
    uint32_t current_steps;         // 当前步数
    uint8_t enabled;                // 使能状态
} stepper_motor_t;

// 全局变量声明
extern stepper_motor_t stepper_motor;
extern volatile uint8_t step_flag;

// 函数声明
void Stepper_Motor_Init(void);
void Stepper_Motor_Enable(uint8_t enable);
void Stepper_Motor_Set_Direction(stepper_direction_t dir);
void Stepper_Motor_Set_Speed_RPM(float rpm);
void Stepper_Motor_Set_Speed_Delay(uint32_t delay_us);
void Stepper_Motor_Start_Continuous(void);
void Stepper_Motor_Stop(void);
void Stepper_Motor_Step_Single(void);
void Stepper_Motor_Move_Steps(uint32_t steps, stepper_direction_t dir);
void Stepper_Motor_Move_Angle(float angle, stepper_direction_t dir);

// 定时器中断处理函数
void Stepper_Timer_IRQHandler(void);

// 工具函数
uint32_t Stepper_RPM_To_Delay(float rpm);
float Stepper_Delay_To_RPM(uint32_t delay_us);

#endif /* __STEPPER_MOTOR_H__ */
