#ifndef __STEPPER_MOTOR_H__
#define __STEPPER_MOTOR_H__

#include "ti_msp_dl_config.h"
#include "empty.h"

// Emm_V5.0步进电机驱动器UART控制
#define EMM_V5_UART_INST     UART2_INST        // 使用UART2
#define EMM_V5_DEVICE_ADDR   0x01              // 驱动器地址
#define EMM_V5_CHECKSUM      0x6B              // 固定校验字节

// Emm_V5.0命令定义
#define EMM_CMD_ENABLE       0xF3              // 使能控制
#define EMM_CMD_SPEED        0xF6              // 速度模式
#define EMM_CMD_POSITION     0xFD              // 位置模式
#define EMM_CMD_STOP         0xFE              // 立即停止
#define EMM_CMD_SYNC         0xFF              // 同步运动

// 方向定义
typedef enum {
    EMM_DIR_CW  = 0,    // 顺时针
    EMM_DIR_CCW = 1     // 逆时针
} emm_direction_t;

// 步进电机状态
typedef enum {
    EMM_STATE_IDLE = 0,     // 空闲
    EMM_STATE_RUNNING,      // 运行中
    EMM_STATE_STOPPED       // 停止
} emm_state_t;

// Emm_V5.0控制结构体
typedef struct {
    uint8_t device_addr;        // 设备地址
    uint16_t current_speed;     // 当前速度(RPM)
    emm_direction_t direction;  // 转动方向
    emm_state_t state;          // 当前状态
    uint8_t enabled;            // 使能状态
    uint8_t acceleration;       // 加速度档位(0-255)
} emm_v5_motor_t;

// 全局变量声明
extern emm_v5_motor_t emm_motor;

// 函数声明
void EMM_V5_Init(void);
void EMM_V5_Enable(uint8_t enable);
void EMM_V5_Set_Speed_Mode(emm_direction_t dir, uint16_t rpm, uint8_t acceleration);
void EMM_V5_Set_Position_Mode(emm_direction_t dir, uint16_t rpm, uint8_t acceleration, uint32_t pulses);
void EMM_V5_Stop_Immediately(void);
uint8_t EMM_V5_Send_Command(uint8_t *cmd, uint8_t len);
void EMM_V5_Start_Continuous_Rotation(emm_direction_t dir, uint16_t rpm);

// UART发送接收函数
void EMM_V5_UART_Send(uint8_t *data, uint8_t len);
uint8_t EMM_V5_UART_Receive(uint8_t *data, uint8_t max_len, uint32_t timeout_ms);

#endif /* __STEPPER_MOTOR_H__ */
